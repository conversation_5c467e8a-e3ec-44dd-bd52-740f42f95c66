# service.py
import traceback
import numpy as np
from bson import ObjectId
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.et_data_set import EtDataSet
from com.exturing.ai.test.model.test_report import AccuracyReportModel, AccuracyReportQueryModel, AccuracyReportGroupQueryModel
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.model.test_task_result import TestTaskResult
from com.exturing.ai.test.model.test_result import TestResult
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.service.test_config_service import find_pk


# 评测结果准确率统计
def query_accuracy_rate(data: AccuracyReportQueryModel) -> str:
    result_res = MongoDBUtil.find_condition(TestTaskResult._doc, data.get_result_condition())
    et_log.info(f"query_accuracy_rate result_res:{result_res}")
    et_log.info(f"query_accuracy_rate result_res:{result_res}")
    result_list = list(result_res or [])
    if len(result_list) == 0:
        return None
    
    result_item_res = MongoDBUtil.find_condition(TestResult._doc, data.get_result_item_condition(result_list))
    result_item_total_list = list(result_item_res or [])
    
    report_list = []

    for result in result_list:
        task_id = str(result.get("task_id", ""))
        task_result_id = str(result.get("_id", ""))
        report_time = result.get("create_time", "")

        result_item_list = [item for item in result_item_total_list if str(item["task_result_id"]) == str(result["_id"])]
        result_item_list_json = [MongoDBUtil.serialize_document(doc) for doc in result_item_list]
        res = calc_accuracy_rate(result_item_list_json)
        category_res = calc_accuracy_rate(result_item_list_json, type='category')
        answer_res = calc_accuracy_rate(result_item_list_json, type='answer')
        task_res = calc_accuracy_rate(result_item_list_json, type='task')

        report = AccuracyReportModel(task_id=task_id, 
                                     task_result_id=task_result_id,
                                     report_time=report_time, 
                                     result_item_count=res["result_item_count"],
                                     pass_count=res["pass_count"],
                                     ad_pass_count=res["ad_pass_count"],
                                     accuracy_rate=res["accuracy_rate"], 
                                     ad_accuracy_rate=res["ad_accuracy_rate"],
                                     category_pass_count=category_res["pass_count"],
                                     ad_category_pass_count=category_res["ad_pass_count"],
                                     category_accuracy_rate=category_res["accuracy_rate"],
                                     ad_category_accuracy_rate=category_res["ad_accuracy_rate"],
                                     answer_pass_count=answer_res["pass_count"],
                                     ad_answer_pass_count=answer_res["ad_pass_count"],
                                     answer_accuracy_rate=answer_res["accuracy_rate"],
                                     ad_answer_accuracy_rate=answer_res["ad_accuracy_rate"],
                                     task_pass_count=task_res["pass_count"],
                                     ad_task_pass_count=task_res["ad_pass_count"],
                                     task_accuracy_rate=task_res["accuracy_rate"],
                                     ad_task_accuracy_rate=task_res["ad_accuracy_rate"],)
        
        report_list.append(report.model_dump())
        
    return report_list

# 评测结果准确率分组统计
def query_accuracy_rate_group(data: AccuracyReportGroupQueryModel):
    result_item_res = MongoDBUtil.find_condition(TestResult._doc, data.get_result_item_condition())
    result_item_list = list(result_item_res or [])
    result_item_list_json = [MongoDBUtil.serialize_document(doc) for doc in result_item_list]

    grouped_data = {"all": []}

    # 按维度分组
    if data.group_by == "dimension_id":
        for item in result_item_list_json:
            grouped_data["all"].append(item)
            dimension_id = item.get("dimension_id", "") 
            if dimension_id not in grouped_data:
                grouped_data[dimension_id] = []

            grouped_data[dimension_id].append(item)
    
    # 按数据标签分组
    elif data.group_by == 'data_tag_id': 
        for item in result_item_list_json:
            grouped_data["all"].append(item)
            data_tags = item.get("data_tags", "") 
            data_tag_ids = data_tags.split(",")
            for data_tag_id in data_tag_ids:
                if data_tag_id not in grouped_data:
                    grouped_data[data_tag_id] = []

                grouped_data[data_tag_id].append(item)

    result_list = []
    for item in grouped_data:
        item_list = grouped_data[item]
        res = calc_accuracy_rate(item_list)
        category_res = calc_accuracy_rate(item_list, type='category')
        answer_res = calc_accuracy_rate(item_list, type='answer')
        task_res = calc_accuracy_rate(item_list, type='task')

        report = AccuracyReportModel(task_id=data.task_id,
                                     task_result_id=data.task_result_id,
                                     group_id=item,
                                     result_item_count=res["result_item_count"],
                                     pass_count=res["pass_count"],
                                     ad_pass_count=res["ad_pass_count"],
                                     accuracy_rate=round(res["accuracy_rate"], 2),
                                     ad_accuracy_rate=round(res["ad_accuracy_rate"], 2),
                                     category_pass_count=category_res["pass_count"],
                                     ad_category_pass_count=category_res["ad_pass_count"],
                                     category_accuracy_rate=category_res["accuracy_rate"],
                                     ad_category_accuracy_rate=category_res["ad_accuracy_rate"],
                                     answer_pass_count=answer_res["pass_count"],
                                     ad_answer_pass_count=answer_res["ad_pass_count"],
                                     answer_accuracy_rate=round(answer_res["accuracy_rate"], 2),
                                     ad_answer_accuracy_rate=round(answer_res["ad_accuracy_rate"], 2),
                                     task_pass_count=task_res["pass_count"],
                                     ad_task_pass_count=task_res["ad_pass_count"],
                                     task_accuracy_rate=round(task_res["accuracy_rate"], 2),
                                     ad_task_accuracy_rate=round(task_res["ad_accuracy_rate"], 2),)
        result_list.append(report.model_dump())
        
    return result_list
    
# 计算准确率(实际准确率、标定后准确率)
def calc_accuracy_rate(result_item_list, type='final'):
    # 最终结果
    result_key = 'result_final'
    ad_result_key = 'ad_result_final'

    if type == 'category':
        # 落域结果
        result_key = 'result_category'
        ad_result_key = 'ad_result_category'
    if type == 'answer':
        # 回答结果
        result_key = 'result_answer'
        ad_result_key = 'ad_result_answer'
    if type == 'task':
        # 任务型结果
        result_key = 'result_task'
        ad_result_key = 'ad_result_task'

    # 过滤出通过的列表
    pass_list = [item for item in result_item_list if item[result_key] == 1]
    # 过滤出标定通过的列表
    ad_pass_list = [item for item in result_item_list if item[ad_result_key] == 1]

    result_item_count = len(result_item_list)
    pass_count = len(pass_list)
    ad_pass_count = len(ad_pass_list)

    # 实际准确率
    accuracy_rate = pass_count / result_item_count * 100 if result_item_count > 0 else 0
    # 标定后准确率
    ad_accuracy_rate = ad_pass_count / result_item_count * 100 if result_item_count > 0 else 0

    return {
        "result_item_count": result_item_count,
        "pass_count": pass_count,
        "ad_pass_count": ad_pass_count,
        "accuracy_rate": accuracy_rate, 
        "ad_accuracy_rate": ad_accuracy_rate
    }

def get_superCLUE_auto_report(plan_id):
    """
    根据计划id，获取计划对应的任务（按评测集分组，最新的任务），最近一次的评测结果
    :param plan_id: 计划id
    :return: {"result_id":"结果id", "dataset_name": "评测集名称", "dataset_id":"评测集id", "config_name": "通道名称",
                    "config_id": "通道id", "avg_score": "平均分", "total_use_time": "总耗时"}
    """
    et_log.info(f"get_superCLUE_auto_report plan_id:{plan_id}")
    try:
        # 根据任务id，按评测集分组，获取每组最新的任务id
        task_id_groups = TestTask.query_new_id_by_dataset(plan_id)
        et_log.info(f"get_superCLUE_auto_report task_id_groups:{task_id_groups}")
        if not task_id_groups or len(task_id_groups) == 0:
            et_log.error(f"get_superCLUE_auto_report plan_id:{plan_id} not found new_task_ids:{task_id_groups}")
            return []
        # 根据最新的任务id组合，获取每个任务最新的任务结果记录id
        task_ids = []
        for data in task_id_groups:
            if "task_id" not in data or len(data.get("task_id", "")) == 0:
                continue
            task_ids.append(data.get("task_id", ""))
        et_log.info(f"get_superCLUE_auto_report task_ids:{task_ids}")
        task_result_groups = TestTaskResult.query_new_id_by_task_ids(task_ids)
        et_log.info(f"get_superCLUE_auto_report task_result_groups:{task_result_groups}")
        if not task_result_groups or len(task_result_groups) == 0:
            et_log.error(f"get_superCLUE_auto_report plan_id:{plan_id} not found task_result_groups:{task_result_groups}")
            return []
        # 根据任务结果记录id,获取对应的记录
        result_ids = []
        for data in task_result_groups:
            if "result_id" not in data or len(data.get("result_id", "")) == 0:
                continue
            result_ids.append(data.get("result_id", ""))
        et_log.info(f"get_superCLUE_auto_report result_ids:{result_ids}")
        object_ids = [ObjectId(result_id) for result_id in result_ids]
        result_condition = {"_id":{"$in": object_ids}}
        result_count = TestTaskResult.find_condition_count(result_condition)
        task_results = TestTaskResult.find_condition(result_condition, None, result_count, 0)
        if not task_results or len(task_results) == 0:
            et_log.error(f"get_superCLUE_auto_report plan_id:{plan_id} not found task_results:{task_results}")
            return []
        # 拼装报表数据集合
        results = []
        for result in task_results:
            if not result.data_set_id or len(str(result.data_set_id)) == 0:
                continue
            dataset_id = str(result.data_set_id)
            dataset = EtDataSet.find_by_pk(dataset_id)
            if not dataset or "name" not in dataset:
                continue

            if not result.eval_config_id or len(str(result.eval_config_id)) == 0:
                continue
            conf_id = str(result.eval_config_id)
            config_dict = find_pk(conf_id)
            if not config_dict or "name" not in config_dict:
                continue

            data = {"result_id":str(result.id), "dataset_name": dataset.get("name", ""), "dataset_id":dataset_id, "config_name": config_dict.get("name", ""),
                    "config_id": conf_id, "avg_score": result.avg_score, "total_use_time": result.total_use_time}
            results.append(data)
        et_log.info(f"get_superCLUE_auto_report results:{results}")
        return results
    except Exception as e:
        et_log.error(f"get_superCLUE_auto_report exception:{e}\n{traceback.print_stack()}")
        return []

def get_radar_chart_data(task_result_id=None, task_id=None, group_by=None):
    """
    获取雷达图数据，只包括准确率、召回率、F1值三个核心指标
    :param task_result_id: 任务结果ID（优先使用）
    :param task_id: 任务ID（当task_result_id不存在时使用）
    :return: 雷达图数据格式 {"任务ID": str, "accuracy": float, "recall": float, "f1_score": float}
    """
    et_log.info(f"get_radar_chart_data task_result_id:{task_result_id}, task_id:{task_id}, group_by:{group_by}")

    try:
        def extract_metrics(result):
            return {
                "task_result_id": str(result.get("_id", "")),
                "task_id": str(result.get("task_id", "")),
                "rate_final": round(float(result.get("rate_final", 0)), 2),
                "rate_recall": round(float(result.get("rate_recall", 0)), 2),
                "rate_category": round(float(result.get("rate_category", 0)), 2)
            }

        task_result = None

        if task_result_id:
            task_result = TestTaskResult.find_by_pk(task_result_id)
            et_log.info(f"Query by task_result_id result: {bool(task_result)}")

        if not task_result and task_id:
            # 使用正确的查询方法，按创建时间倒序获取最新的任务结果
            condition = {"task_id": ObjectId(task_id), "is_del": 0}
            results = TestTaskResult.find_condition(condition, [("create_time", -1)], 1, 0)
            if results and len(results) > 0:
                task_result = results[0].to_json_str()
                et_log.info(f"Found latest result by task_id: {task_result.get('_id')}")

        return extract_metrics(task_result) if task_result else {}

    except Exception as e:
        et_log.error(f"Error in get_radar_chart_data: {str(e)}\n{traceback.format_exc()}")
        return {}

#各模块|维度柱状图
# -------------------------------返回--------------------------------------
def get_dimension_metrics_chart_data(task_result_id=None, task_id=None, group_by=None):
    """
    获取按指定分组方式的准确率、召回率、F1 柱状图数据
    :param task_result_id: 任务结果ID（优先使用）
    :param task_id: 任务ID（当task_result_id不存在时使用）
    :param group_by: 分组方式 'dimension_id'=按维度分组, 'data_tag_id'=按标签分组, None=不分组
    :return: list[dict]
    """
    et_log.info(f"get_dimension_metrics_chart_data task_result_id:{task_result_id}, "
                f"task_id:{task_id}, group_by:{group_by}")

    try:
        # 性能优化：确保总是有task_result_id进行精确查询
        actual_task_result_id = task_result_id
        actual_task_id = task_id

        # 如果没有task_result_id，尝试获取最新的task_result_id
        if not actual_task_result_id:
            if not actual_task_id:
                et_log.error("Neither task_result_id nor task_id provided")
                return []

            et_log.info(f"Only task_id provided, finding latest task_result_id for task_id: {actual_task_id}")
            condition = {"task_id": ObjectId(actual_task_id), "is_del": 0}
            results = TestTaskResult.find_condition(condition, [("create_time", -1)], 1, 0)
            if results and len(results) > 0:
                actual_task_result_id = str(results[0]._id)
                et_log.info(f"Found latest task_result_id: {actual_task_result_id}")
            else:
                et_log.warning(f"No task results found for task_id: {actual_task_id}, using fallback strategy")
                # 直接使用task_id查询数据
                et_log.warning("Performance may be impacted due to direct task_id query")

                # 直接使用task_id获取数据
                raw = _fetch_raw_data(None, actual_task_id, group_by)
                if not raw:
                    return []

                task_info, result_items, dataset_items, group_names = raw
                group_stats = _group_statistics(result_items, dataset_items, group_by, include_empty=True)

                chart_data = []
                for gid, st in group_stats.items():
                    if st["result_count"] == 0 and st["dataset_count"] == 0:
                        continue

                    name = group_names.get(gid, gid)
                    acc, rec, f1 = _calculate_metrics(st)

                    chart_data.append({
                        "task_result_id": "",  # 无task_result_id
                        "task_id": actual_task_id,
                        "rate_final": acc,
                        "rate_recall": rec,
                        "rate_category": f1,
                        "group_name": name,
                        "group_by": group_by,
                        #"fallback_mode": True  # 标记为降级模式
                    })

                et_log.info(f"Fallback query completed with {len(chart_data)} items")
                return chart_data

        if group_by is None or group_by == '':
            group_by = 'dimension_id'
            et_log.info(f"Using default group_by: {group_by}")

        # 使用task_result_id获取数据，确保高性能查询
        raw = _fetch_raw_data(actual_task_result_id, None, group_by)
        if not raw:
            return []

        task_info, result_items, dataset_items, group_names = raw
        group_stats = _group_statistics(result_items, dataset_items, group_by, include_empty=True)

        # 确保返回正确的ID字段
        if not actual_task_id and task_info:
            actual_task_id = str(task_info.get("_id", ""))

        chart_data = []
        for gid, st in group_stats.items():
            if st["result_count"] == 0 and st["dataset_count"] == 0:
                continue

            name = group_names.get(gid, gid)
            acc, rec, f1 = _calculate_metrics(st)

            chart_data.append({
                "task_result_id": actual_task_result_id,
                "task_id": actual_task_id,
                "rate_final": acc,
                "rate_recall": rec,
                "rate_category": f1,
                "group_name": name,
                "group_by": group_by,
            })

            et_log.info(f"{name} | 测试:{st['result_count']} 通过:{st['result_true_count']} "
                        f"数据集:{st['dataset_count']} | Acc:{acc}% Rec:{rec}% F1:{f1}%")

        et_log.info(f"get_dimension_metrics_chart_data result: {chart_data}")
        return chart_data

    except Exception as e:
        et_log.error(f"get_dimension_metrics_chart_data exception: {e}\n{traceback.format_exc()}")
        return []

# ---------------------------读取数据-------------------------------------
def _fetch_raw_data(task_result_id, task_id, group_by='dimention_id'):
    """
    获取任务信息、测试结果、数据集明细
    返回  (task_info, result_items, dataset_items, group_names)
    任一环节失败 => 返回 None
    """
    task_info, dataset_id = get_task_and_dataset_info(task_result_id, task_id)
    if not (task_info and dataset_id):
        et_log.error("无法获取任务信息或数据集ID")
        return None

    result_items = get_result_items(task_result_id, task_id)
    if not result_items:
        et_log.info("没有找到测试结果明细数据")
        return None

    dataset_items = get_dataset_items(dataset_id)
    group_names = get_group_names(group_by)
    return task_info, result_items, dataset_items, group_names

#-----------------------获取统计结果------------------------------
def _group_statistics(result_items, dataset_items, group_by, include_empty=True):
    """
    按维度或标签做计数：
    result_count / result_true_count / dataset_count
    """
    stats = {}
    # ---------- 维度 ----------
    # 统计测试结果
    if group_by == 'dimension_id':
        for ri in result_items:
            raw_dim = ri.get("dimension_id")
            gid = "empty" if not raw_dim or str(raw_dim).strip() in ["", "null", "None"] else str(raw_dim).strip()
            #gid = str(ri.get("dimension_id", "")) or "empty"
            if include_empty or gid != "empty":
                stats.setdefault(gid, {"result_count": 0, "result_true_count": 0, "dataset_count": 0})
                stats[gid]["result_count"] += 1
                if ri.get("result_final") == 1:
                    stats[gid]["result_true_count"] += 1
    #统计数据集结果
        for di in dataset_items:
            raw_dim = di.get("dimension_id")  
            gid = "empty" if not raw_dim or str(raw_dim).strip() in ["", "null", "None"] else str(raw_dim).strip()
            if include_empty or gid != "empty":
                stats.setdefault(gid, {"result_count": 0, "result_true_count": 0, "dataset_count": 0})
                stats[gid]["dataset_count"] += 1

    # ---------- 标签 ----------
    elif group_by == 'data_tag_id':
        for ri in result_items:
            tags = str(ri.get("data_tags", "")) or ""
            for gid in ([t.strip() for t in tags.split(",") if t.strip()] or ["empty"]):
                if include_empty or gid != "empty":
                    stats.setdefault(gid, {"result_count": 0, "result_true_count": 0, "dataset_count": 0})
                    stats[gid]["result_count"] += 1
                    if ri.get("result_final") == 1:
                        stats[gid]["result_true_count"] += 1

        for di in dataset_items:
            tags = str(di.get("item_tags", "")) or ""
            for gid in ([t.strip() for t in tags.split(",") if t.strip()] or ["empty"]):
                if include_empty or gid != "empty":
                    stats.setdefault(gid, {"result_count": 0, "result_true_count": 0, "dataset_count": 0})
                    stats[gid]["dataset_count"] += 1
    return stats

# -----------------------计算指标-----------------------------------
def _calculate_metrics(st):
    rc, tc, dc = st["result_count"], st["result_true_count"], st["dataset_count"]
    acc = round(tc / rc * 100, 2) if rc else 0
    rec = round(tc / dc * 100, 2) if dc else 0
    f1  = 0 if acc + rec == 0 else round(2 * acc * rec / (acc + rec), 2)
    return acc, rec, f1

# 工具函数
#---------------- 获取任务信息和数据集ID------------------
def get_task_and_dataset_info(task_result_id, task_id):
    if task_result_id:
        tr = TestTaskResult.find_by_pk(task_result_id)
        if tr:
            ti = TestTask.find_by_pk(str(tr.get("task_id")))
            return ti, ti.get("data_set_id") if ti else None
    elif task_id:
        ti = TestTask.find_by_pk(task_id)
        return ti, ti.get("data_set_id") if ti else None
    return None, None

#--------------- 获取测试结果明细数据---------------------
def get_result_items(task_result_id, task_id):
    """
    获取测试结果明细数据
    优先使用task_result_id进行精确查询以保证性能
    """
    #start_time = time.time()
    cond = {"is_del": 0}
    if task_result_id:
        # 优先使用task_result_id，性能最佳
        cond["task_result_id"] = ObjectId(task_result_id)
        et_log.info(f"[PERF] Querying test results by task_result_id: {task_result_id}")
    elif task_id:
        # 使用task_id查询
        et_log.warning(f"[PERF] Querying test results by task_id: {task_id} - this may impact performance")
        cond["task_id"] = ObjectId(task_id)
    else:
        et_log.error("Neither task_result_id nor task_id provided for get_result_items")
        return []

    # 只查询需要的字段，减少网络传输和内存使用
    projection = {
        "_id": 1,
        "task_result_id": 1,
        "task_id": 1,
        "dimension_id": 1,
        "data_tags": 1,
        "result_final": 1,
        "is_del": 1
    }

    # 使用projection优化查询
    cursor = MongoDBUtil.mdb[TestResult._doc].find(cond, projection)
    results = list(cursor)

    et_log.info(f"Found {len(results)} test result items")
    return results

#--------------- 获取数据集明细数据---------------------
def get_dataset_items(dataset_id):
    #start_time = time.time()
    et_log.info(f"[PERF] Querying dataset items for dataset_id: {dataset_id}")

    # 只查询需要的字段
    projection = {
        "_id": 1,
        "data_set_id": 1,
        "dimension_id": 1,
        "item_tags": 1,
        "is_del": 1
    }

    condition = {"data_set_id": ObjectId(dataset_id), "is_del": 0}
    # 添加权限过滤
    match_oem_codes = MongoDBUtil.oem_codes_condition()
    condition["oem_codes"] = match_oem_codes

    cursor = MongoDBUtil.mdb[EtDataSetItem._doc].find(condition, projection)
    results = list(cursor)

    et_log.info(f"Found {len(results)} dataset items")
    return results
#--------------- 获取分组名称---------------------
def get_group_names(group_by):
    """
    根据分组类型获取对应的名称映射
    """
    et_log.info(f"[PERF] Getting group names for group_by: {group_by}")
    names = {}
    if group_by == 'dimension_id':
        from com.exturing.ai.test.service.dic_service import DicService
        dimensions = DicService.query_list(None, None, "dm-dimension") or []
       
        for d in dimensions:
            # 使用dimension_id字段的实际值作为键
            dim_id = d.get("dimension_id", "")
            names[dim_id] = d.get("name", f"维度_{dim_id}")
        names["empty"] = "无维度"   
    elif group_by == 'data_tag_id':
        from com.exturing.ai.test.model.data_tag import DataTagModel
        tags = MongoDBUtil.find_condition(DataTagModel._doc, {"is_del": 0}) or []
       
        for t in tags:
            # 使用data_tags字段的实际值作为键
            tag_value = t.get("data_tags", "")
            names[tag_value] = t.get("tag_name", f"标签_{tag_value}")
        names["empty"] = "无标签"
    else:
        # 如果不是已知的分组类型，返回空字典
        et_log.warning(f"Unknown group_by type: {group_by}")
    return names

def get_category_metrics_line_chart_data(task_result_ids=None, task_ids=None):
    """
    获取多个结果的中枢分类（落域）准确率、召回率、F1分数对比折线图数据
    """
    et_log.info(f"get_category_metrics_line_chart_data task_result_ids:{task_result_ids}, task_ids:{task_ids}")
    line_chart_data = []

    ids_source =  task_ids or []
    if not ids_source:
        et_log.error("Neither task_result_ids nor task_ids provided")
        return []

    if task_ids:
        for task_id in task_ids:
            data = _get_single_metrics_unified(None, task_id, mode='category')
            if data:
                line_chart_data.extend(data)

    line_chart_data.sort(key=lambda x: x["report_time"])
    return line_chart_data

def get_dimension_metrics_line_chart_data(task_result_ids=None, task_ids=None, group_by=None, group_name=None):
    """
    获取某个分类（各模块\维度）在多个结果中的准确率、召回率、F1分数对比折线图数据
    """
    et_log.info(f"[Start] get_dimension_metrics_line_chart_data | task_ids: {task_ids}, group_by: {group_by}")

    line_chart_data = []

    if not task_ids:
        et_log.error("[Error] 参数缺失：task_id")
        return []

    # 将空字符串当作默认维度分组
    if not group_by or str(group_by).strip() == "":
        group_by = "dimension_id"  # 默认按维度分组
        et_log.info("[Info] 未指定group_by，默认使用 'dimension_id'")

    # 处理 task_id
    if task_ids:
        for task_id in task_ids:
            try:
                et_log.debug(f"[Info] 正在处理 task_id: {task_id}")
                data = _get_single_metrics_unified(None, task_id, mode='dimension', group_by=group_by)

                if data:
                    if isinstance(data, list):
                        et_log.debug(f"[Info] task_id: {task_id} 返回 {len(data)} 项")
                        line_chart_data.extend(data)
                    else:
                        et_log.debug(f"[Info] task_id: {task_id} 返回单项数据")
                        line_chart_data.append(data)
                else:
                    et_log.warning(f"[Warn] task_id: {task_id} 无返回数据")

            except Exception as e:
                et_log.exception(f"[Exception] 获取 task_id: {task_id} 的指标失败：{e}")

    # 排序
    line_chart_data.sort(key=lambda x: x.get("report_time", ""))
    et_log.info(f"[Done] 返回总数据量: {len(line_chart_data)} 项")
    return line_chart_data

def _get_single_metrics_unified(task_result_id, task_id, mode='category', group_by=None):

    try:
        if mode == 'category':
            # 中枢分类模式：使用汇总数据
            return _get_category_metrics_from_summary(task_result_id, task_id)
        elif mode == 'dimension':
            # 维度分组模式：复用现有逻辑
            return get_metrics_by_group_under_task_id(task_id, group_by)
        else:
            et_log.error(f"Unknown mode: {mode}")
            return None
    except Exception as e:
        et_log.error(f"Error in _get_single_metrics_unified: {str(e)}\n{traceback.format_exc()}")
        return None

def _get_category_metrics_from_summary(task_result_id, task_id):
    """
    获取中枢分类指标
    """
    task_results = []
    if task_result_id:
        tr = TestTaskResult.find_by_pk(task_result_id)
        if tr:
            task_results = [tr]
    else:
        cond = {"task_id": ObjectId(task_id), "is_del": 0}
        task_results = TestTaskResult.find_condition(cond, [("create_time", -1)], 300, 0)

    if not task_results:
        et_log.warning(f"No task result for {task_result_id or task_id}")
        return None

    task = TestTask.find_by_pk(task_result_id or task_id)
    base_name = task.get("name") if task and task.get("name") else f"任务_{task_id or (task_results[0].get('task_id'))}"

    results = []
    for tr in task_results:
        if hasattr(tr, 'to_json_str'):
            tr = tr.to_json_str()
        elif not isinstance(tr, dict):
            tr = dict(tr)

        date_part = tr.get("create_time", "")[:10]
        series_name = f"{base_name}({date_part})" if date_part else base_name

        result = {
            "series_name": series_name,
            "task_result_id": str(tr["_id"]),
            "task_id": str(tr["task_id"]),
            "rate_final": round(float(tr.get("rate_final", 0)), 2),
            "rate_recall": round(float(tr.get("rate_recall", 0)), 2),
            "rate_category": round(float(tr.get("rate_category", 0)), 2),
            "report_time": tr.get("create_time", ""),
        }
        results.append(result)

    return results

def get_metrics_by_group_under_task_id(task_id, group_by):
    """
    获取指定 task_id 下所有 task_result 中的维度（group_by）指标数据，返回所有分组。
    """
    et_log.info(f"[START] get_metrics_by_group_under_task_id for task_id={task_id}, group_by={group_by}")

    cond = {"task_id": ObjectId(task_id), "is_del": 0}
    all_task_results = TestTaskResult.find_condition(cond, [("create_time", -1)], 100, 0)

    if not all_task_results:
        et_log.warning(f"[WARN] No task results found for task_id: {task_id}")
        return []

    task = TestTask.find_by_pk(task_id)
    base_name = task.get("name") if task and task.get("name") else f"任务_{task_id}"

    result_list = []
    for tr in all_task_results:
        if hasattr(tr, 'to_json_str'):
            tr = tr.to_json_str()
        elif not isinstance(tr, dict):
            tr = dict(tr)

        task_result_id = str(tr.get("_id"))
        create_time = tr.get("create_time", "")
        date_part = create_time[:10]
        series_name = f"{base_name}({date_part})" if date_part else base_name

        et_log.info(f"[INFO] Processing task_result_id={task_result_id}, series_name={series_name}")

        try:
            chart_data = _cached_dimension_chart_data(task_result_id, group_by)
        except Exception as e:
            et_log.error(f"[ERROR] Failed to get chart data for task_result_id={task_result_id}: {e}")
            continue

        if not chart_data:
            et_log.warning(f"[WARN] No chart data found for task_result_id={task_result_id}")
            continue

        for item in chart_data:
            result_list.append({
                "series_name": series_name,
                "task_result_id": task_result_id,
                "task_id": task_id,
                "group_name": item.get("group_name", ""),  # 可选保留
                "rate_final": item.get("rate_final", 0),
                "rate_recall": item.get("rate_recall", 0),
                "rate_category": item.get("rate_category", 0),
                "report_time": create_time,
            })

    et_log.info(f"[DONE] Finished get_metrics_by_group_under_task_id for task_id={task_id}, total items={len(result_list)}")
    return result_list


from functools import lru_cache

@lru_cache(maxsize=1024)
def _cached_dimension_chart_data(task_result_id: str, group_by: str):
    return get_dimension_metrics_chart_data(task_result_id=task_result_id, group_by=group_by)

def get_dimension_pie_chart_data(task_id=None, data_set_id=None, group_by='dimension_id'):
    """
    获取维度分组饼状图数据
    :param task_id: 任务ID
    :param data_set_id: 数据集ID
    :param group_by: 分组方式 'dimension_id'=按维度分组, 'data_tag_id'=按标签分组
    :return: list[dict] 包含分组名称和数量的列表
    """
    et_log.info(f"get_dimension_pie_chart_data task_id:{task_id}, data_set_id:{data_set_id}, group_by:{group_by}")
    
    try:
        # 获取数据集ID
        actual_data_set_id = data_set_id
        if not actual_data_set_id and task_id:
            task_info = TestTask.find_by_pk(task_id)
            if task_info:
                actual_data_set_id = task_info.get("data_set_id")
        
        if not actual_data_set_id:
            et_log.error("无法获取数据集ID")
            return []
        
        # 获取数据集明细数据
        dataset_items = get_dataset_items(actual_data_set_id)
        if not dataset_items:
            et_log.info("没有找到数据集明细数据")
            return []
        
        # 获取分组名称映射
        group_names = get_group_names(group_by)
        
        # 统计各分组数量
        group_counts = {}
        
        if group_by == 'dimension_id':
            for item in dataset_items:
                raw_dim = item.get("dimension_id")
                gid = "empty" if not raw_dim or str(raw_dim).strip() in ["", "null", "None"] else str(raw_dim).strip()
                group_counts[gid] = group_counts.get(gid, 0) + 1
                
        elif group_by == 'data_tag_id':
            for item in dataset_items:
                tags = str(item.get("item_tags", "")) or ""
                tag_list = [t.strip() for t in tags.split(",") if t.strip()] or ["empty"]
                for gid in tag_list:
                    group_counts[gid] = group_counts.get(gid, 0) + 1
        
        # 构建返回数据
        pie_data = []
        for gid, count in group_counts.items():
            if count > 0:
                name = group_names.get(gid, gid)
                pie_data.append({
                    "group_id": gid,
                    "group_name": name,
                    "count": count,
                    "group_by": group_by
                })
        
        # 按数量降序排序
        pie_data.sort(key=lambda x: x["count"], reverse=True)
        
        et_log.info(f"get_dimension_pie_chart_data result: {len(pie_data)} groups")
        return pie_data
        
    except Exception as e:
        et_log.error(f"get_dimension_pie_chart_data exception: {e}\n{traceback.format_exc()}")
        return []


def get_execution_time_statistics(task_id=None, task_result_id=None):
    """
    获取模型执行时间统计信息（不分组）
    :param task_id: 任务ID（可选）
    :param task_result_id: 任务结果ID（可选）
    :return: 统计信息字典，包含首帧时间和总时间的统计信息
    """
    et_log.info(f"get_execution_time_statistics task_id:{task_id} task_result_id:{task_result_id}")
    try:
        # 构建查询条件
        condition = {"is_del": 0}
        
        if task_id:
            condition["task_id"] = ObjectId(task_id)
        elif task_result_id:
            condition["task_result_id"] = ObjectId(task_result_id)
        else:
            et_log.error("get_execution_time_statistics error: task_id or task_result_id must be provided")
            return None
        
        # 查询测试结果数据
        result_list = TestResult.find_condition(condition, None, 10000, 0)  # 限制查询数量避免性能问题
        if not result_list or len(result_list) == 0:
            et_log.error(f"get_execution_time_statistics not found data for condition: {condition}")
            return None
        
        # 计算统计信息
        return _calculate_execution_time_statistics_simple(result_list)
        
    except Exception as e:
        et_log.error(f"get_execution_time_statistics exception:{e}")
        traceback.print_exc()
        return None

def get_execution_time_statistics_grouped(task_id=None, task_result_id=None, group_by=None):
    """
    获取模型执行时间统计信息（按模块分组）
    :param task_id: 任务ID（可选）
    :param task_result_id: 任务结果ID（可选）
    :param group_by: 分组字段，支持 'dimension_id', 'data_set_id'
    :return: 统计信息字典，包含首帧时间和总时间的统计信息，支持分组
    """
    et_log.info(f"get_execution_time_statistics_grouped task_id:{task_id} task_result_id:{task_result_id} group_by:{group_by}")
    try:
        # 构建查询条件
        condition = {"is_del": 0}
        
        if task_id:
            condition["task_id"] = ObjectId(task_id)
        elif task_result_id:
            condition["task_result_id"] = ObjectId(task_result_id)
        else:
            et_log.error("get_execution_time_statistics_grouped error: task_id or task_result_id must be provided")
            return None
        
        # 查询测试结果数据
        result_list = TestResult.find_condition(condition, None, 10000, 0)  # 限制查询数量避免性能问题
        if not result_list or len(result_list) == 0:
            et_log.error(f"get_execution_time_statistics_grouped not found data for condition: {condition}")
            return None
        
        # 确定分组字段，默认按 dimension_id 分组
        if group_by is None or group_by.strip() == "":
            group_by = "dimension_id"

        # 按指定字段分组
        grouped_data = _group_data(result_list, group_by)
        
        if not grouped_data:
            et_log.error("get_execution_time_statistics_grouped no valid grouped data found")
            return None
        
        # 计算每个分组的统计信息
        result_data = {}
        
        for group_name, group_results in grouped_data.items():
            group_stats = _calculate_execution_time_statistics_simple(group_results)
            if group_stats:
                result_data[group_name] = group_stats
        
        et_log.info(f"get_execution_time_statistics_grouped grouped result: {result_data}")
        return result_data
        
    except Exception as e:
        et_log.error(f"get_execution_time_statistics_grouped exception:{e}")
        traceback.print_exc()
        return None

def _group_data(result_list, group_by):
    """
    按指定字段分组数据
    :param result_list: TestResult对象列表
    :param group_by: 分组字段，支持 'dimension_id', 'data_set_id'
    :return: 分组后的数据字典
    """
    try:
        grouped_data = {}
        
        for result_item in result_list:
            # 确保 result_item 是一个字典
            if isinstance(result_item, dict):
                result = TestResult(**result_item)
            elif isinstance(result_item, TestResult):
                result = result_item
            else:
                et_log.error(f"Unexpected type for result_item: {type(result_item)}")
                continue
            
            # 获取分组键
            group_key = getattr(result, group_by, None)
            
            # 处理分组键为空的情况
            if group_key is None or str(group_key).strip() in ["", "null", "None"]:
                group_name = "无维度" if group_by == "dimension_id" else "无分组"
            else:
                group_name = str(group_key)  # 使用实际字段值作为分组名称
            
            if group_name not in grouped_data:
                grouped_data[group_name] = []
            
            grouped_data[group_name].append(result)
        
        return grouped_data
    except Exception as e:
        et_log.error(f"_group_data exception:{e}")
        traceback.print_exc()
        return None

def _calculate_execution_time_statistics_simple(result_list):
    """
    计算执行时间统计信息（简化版本，用于单个分组或整体统计）
    :param result_list: TestResult对象列表
    :return: 统计信息字典
    """
    try:
        # 提取首帧时间和总时间数据
        first_res_times = []
        qa_use_times = []
        
        for result_item in result_list:
            # 确保 result_item 是一个字典
            if isinstance(result_item, dict):
                result = TestResult(**result_item)
            elif isinstance(result_item, TestResult):
                result = result_item
            else:
                et_log.error(f"Unexpected type for result_item: {type(result_item)}")
                continue
            
            # 确保 first_res_time 和 qa_use_time 是数值类型
            try:
                first_res_time = float(result.first_res_time) if result.first_res_time else 0
                qa_use_time = float(result.qa_use_time) if result.qa_use_time else 0
            except ValueError as ve:
                et_log.error(f"Invalid time value: first_res_time={result.first_res_time}, qa_use_time={result.qa_use_time}")
                continue
            
            if first_res_time > 0:  # 只统计有效数据
                first_res_times.append(first_res_time)
            if qa_use_time > 0:  # 只统计有效数据
                qa_use_times.append(qa_use_time)
        
        if not first_res_times and not qa_use_times:
            et_log.error("_calculate_execution_time_statistics_simple no valid time data found")
            return None
        
        # 计算统计信息
        result_data = {}
        
        # 首帧时间统计
        if first_res_times:
            first_res_times.sort()
            avg_first_res = np.mean(first_res_times)
            p50_first_res = np.percentile(first_res_times, 50)
            p90_first_res = np.percentile(first_res_times, 90)
            p95_first_res = np.percentile(first_res_times, 95)
            
            # 找到对应的task_id和task_result_id
            p50_index = int(len(first_res_times) * 0.5)
            p90_index = int(len(first_res_times) * 0.9)
            p95_index = int(len(first_res_times) * 0.95)
            
            p50_task_info = _find_task_info_by_time(first_res_times[p50_index], result_list, "first_res_time")
            p90_task_info = _find_task_info_by_time(first_res_times[p90_index], result_list, "first_res_time")
            p95_task_info = _find_task_info_by_time(first_res_times[p95_index], result_list, "first_res_time")
            
            result_data["first_res_time"] = {
                "avg": round(avg_first_res, 2),
                "p50": round(p50_first_res, 2),
                "p90": round(p90_first_res, 2),
                "p95": round(p95_first_res, 2),
                "p50_task_info": p50_task_info,
                "p90_task_info": p90_task_info,
                "p95_task_info": p95_task_info
            }
        
        # 总时间统计
        if qa_use_times:
            qa_use_times.sort()
            avg_qa_use = np.mean(qa_use_times)
            p50_qa_use = np.percentile(qa_use_times, 50)
            p90_qa_use = np.percentile(qa_use_times, 90)
            p95_qa_use = np.percentile(qa_use_times, 95)
            
            # 找到对应的task_id和task_result_id
            p50_task_info = _find_task_info_by_time(qa_use_times[int(len(qa_use_times) * 0.5)], result_list, "qa_use_time")
            p90_task_info = _find_task_info_by_time(qa_use_times[int(len(qa_use_times) * 0.9)], result_list, "qa_use_time")
            p95_task_info = _find_task_info_by_time(qa_use_times[int(len(qa_use_times) * 0.95)], result_list, "qa_use_time")
            
            result_data["qa_use_time"] = {
                "avg": round(avg_qa_use, 2),
                "p50": round(p50_qa_use, 2),
                "p90": round(p90_qa_use, 2),
                "p95": round(p95_qa_use, 2),
                "p50_task_info": p50_task_info,
                "p90_task_info": p90_task_info,
                "p95_task_info": p95_task_info
            }
        
        return result_data
        
    except Exception as e:
        et_log.error(f"_calculate_execution_time_statistics_simple exception:{e}")
        traceback.print_exc()
        return None
    
def _find_task_info_by_time(target_time, result_list, time_field):
    """
    根据时间值找到对应的task_id和task_result_id 
    :param target_time: 目标时间值
    :param result_list: 结果列表
    :param time_field: 时间字段名 ("first_res_time" 或 "qa_use_time")
    :return: 包含task_id和task_result_id的字典
    """
    try:
        for result_item in result_list:
            # 确保 result_item 是一个字典
            if isinstance(result_item, dict):
                result = TestResult(**result_item)
            elif isinstance(result_item, TestResult):
                result = result_item
            else:
                et_log.error(f"Unexpected type for result_item: {type(result_item)}")
                continue
            
            # 确保时间字段是数值类型
            try:
                current_time = float(getattr(result, time_field, 0))
            except ValueError as ve:
                et_log.error(f"Invalid time value for {time_field}: {getattr(result, time_field, 0)}")
                continue
            
            if current_time == target_time:
                return {
                    "task_id": str(result.task_id),
                    "task_result_id": str(result.task_result_id)
                }
        return None
    except Exception as e:
        et_log

