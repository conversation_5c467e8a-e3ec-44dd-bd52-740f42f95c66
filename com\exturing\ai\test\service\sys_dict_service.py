# service.py
from com.exturing.ai.test.model.sys_dict import SysDictModel, SysDictQueryModel
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.service.sys_dict_item_service import _doc as sys_dict_item_doc
from com.exturing.ai.test.comm.log_tool import et_log

_doc = "sys_dict"


# 新增系统字典
def create(data: SysDictModel) -> str:
    data_dict = data.model_dump()
    et_log.info(f"create sys_dict: {data_dict}")
    return MongoDBUtil.insert_one(_doc, data_dict)


# 分页查询系统字典
def query_page(page_num: int = 1, page_size: int = 10, query: SysDictQueryModel = None):
    condition = query.get_query_condition()
    total = MongoDBUtil.find_count(_doc, condition)

    page = PageResult(page_num, page_size, total)

    if total > 0:
        result = MongoDBUtil.find_condition_page(
            _doc, condition, None, page.skip, page_size
        )
        result_list = list(result or [])
        json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]
        page.page_data = json_list

    return page


# 查询系统字典
def query_list(query: SysDictQueryModel = None):
    condition = query.get_query_condition()

    result = MongoDBUtil.find_condition(_doc, condition)
    result_list = list(result or [])
    json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]

    return json_list


# 修改系统字典
def update(id: str, data: SysDictModel) -> bool:
    data_dict = data.model_dump()
    data_dict.pop("code", None)  # 删除code字段，不能修改字典code

    data_dict["_id"] = id
    et_log.info(f"update sys_dict: {data_dict}")
    update_num = MongoDBUtil.update_one_pro(_doc, data_dict)

    if update_num.modified_count and update_num.modified_count > 0:
        return True
    else:
        return False


# 删除系统字典
def delete(id: str) -> bool:
    et_log.info(f"delete sys_dict by id:{id}")

    # 删除该字典下所有字典项数据
    dict_code = MongoDBUtil.find_by_id(_doc, id).get("code")
    MongoDBUtil.delete_by_many(sys_dict_item_doc, {"dict_code": dict_code})

    return MongoDBUtil.delete_by_id(_doc, id, 0) > 0
