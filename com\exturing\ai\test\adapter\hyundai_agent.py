import uuid
from uuid import uuid4
import time
import random
import json
import requests
from com.exturing.ai.test.comm.comm_constant import CTX_USER_ID, CTX_OEM_CODES, SYS_USER
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.test_config import TestConfigInfoModel
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService
import hashlib
import hmac
from typing import Optional
from hashlib import sha256
import base64
import re
from com.exturing.ai.test.model.test_task import TestTask

plugin_template = {
    "query": "",
    "chat_id": "",
    "question_id": "",
    "intent_type": "",
    "intent_log_id": "202504231952255482B2BD40160C039A52",
    "limit": 84,
    "history": [
    ],
    "car_info": {
        "vehicle_name": "掀背车",
        "sound_zone": "enim laboris",
        "audio_name": "居奕辰",
        "audio_producer": "laborum et",
        "current_location": "上海闵行平金中心一号楼",
        "nav_dest_name": "谌国良",
        "nav_dest_address": "北京市海淀区北三环西路甲18号",
        "home_lng_lat": {
            "longitude": 121.474000,
            "latitude": 31.230001
        },
        "battery": 55,
        "tts_language": "id et"
    },
    "plugin_params": "{\"skip_slot\":true,\"city\":\"北京市\",\"area\":\"海淀区\",\"start_date\":\"2025-04-25\",\"end_date\":\"2025-04-25\"}"
}


class Hyundai_car_agent:
    """现代车载agent测试"""

    def __init__(self, config_info: TestConfigInfoModel, item: EtDataSetItem, task_id, result_id, conf_id, do_user,
                 children, task_result_id, oem_codes="", parent_id=""):
        model_params_str = config_info.model_params
        model_params_dict = json.loads(model_params_str)
        device_id = model_params_dict.get("Device_id")
        vehicle_type = model_params_dict.get("Vehicle_Type")
        plugin_url = model_params_dict.get("plugin_url")
        vehicle_id = model_params_dict.get("VEHICLE_ID")
        sing_draw_url = model_params_dict.get("sing_url")
        intent_url = model_params_dict.get("hyundai_intent_url")
        reject_url = model_params_dict.get("hyundai_reject_url")
        self.intent_url = intent_url
        self.reject_url = reject_url
        self.sing_draw_url = sing_draw_url
        self.vehicle_id = vehicle_id
        self.device_id = device_id
        self.vehicle_type = vehicle_type
        self.plugin_url = plugin_url
        self.ak = config_info.model_ak
        self.sk = config_info.model_sk
        self.intent_reject_url = config_info.model_url
        self.prompt = item.question
        self.task_id = task_id
        self.result_id = result_id
        self.conf_id = conf_id
        self.item = item
        self.do_user = do_user
        self.oem_codes = oem_codes
        self.parent_id = parent_id
        self.children = children
        self.task_result_id = task_result_id

    def Hyundai_intent_reject(self):
        """
        现代拒识意图识别
        """
        et_log.info(f"Hyundai:intention task_id:{self.task_id} result_id:{self.result_id} conf_id:{self.conf_id}")
        method = "POST"
        timestamp = int(time.time())
        random_integer = random.randint(0, 65535)
        chat_id = uuid4().hex
        request_id = uuid4().hex
        querystr = f"_timestamp={timestamp}&_nonce={random_integer}&chat_id={chat_id}&request_id={request_id}"
        params = {
            "query": self.prompt
        }
        body = json.dumps(params, ensure_ascii=False)
        sign = self.generate_signature(self.sk, method, querystr, body)
        et_log.info(f"签名生成成功{sign}")
        headers = {
            "X-Signature": f'{self.ak}:{sign}',
            "Device-Id": f"{self.device_id}",
            "content-type": "application/json"
        }
        try:
            start_time = time.time()
            response = requests.post(
                f'{self.intent_reject_url}?{querystr}',
                headers=headers,
                data=body.encode('utf-8'),
                timeout=20
            )
            print(f"rescode{response.status_code}")
            if response.status_code == 200:
                first_info_time = time.time()
                first_res_time = round(float(first_info_time - start_time), 2)
                search_results = response.text
                print(search_results)
                data = json.loads(response.text)
                intent_type = data.get('data', {}).get('intent', {}).get('intent_type', '')
                reject = data.get('data', {}).get('reject', None)
                question_id = data.get('data', {}).get('question_id')
                intent_time = data.get('data', {}).get('debug_info', {}).get('intent_time')
                et_log.info(
                    f"intent_type:{intent_type},reject:{reject}\nquestion_id:{question_id},intent_time{intent_time}")
                return intent_type, reject, question_id, intent_time
            else:
                raise Exception(f"API调用失败，HTTP状态码: {response.status_code}")
        except Exception as e:
            et_log.error(f"API调用失败: {e}")
            return None, None

    def Hyundai_reject(self):
        """
        现代拒识
        """
        reject = True
        try:
            method = "POST"
            timestamp = int(time.time())
            random_integer = random.randint(0, 65535)
            chat_id = uuid4().hex
            request_id = uuid4().hex
            querystr = f"_timestamp={timestamp}&_nonce={random_integer}&chat_id={chat_id}&request_id={request_id}"
            params = {
                "query": self.prompt
            }
            body = json.dumps(params, ensure_ascii=False)
            sign = self.generate_signature(self.sk, method, querystr, body)
            et_log.info(f"签名生成成功{sign}")
            headers = {
                "X-Signature": f'{self.ak}:{sign}',
                "Device-Id": f"{self.device_id}",
                "content-type": "application/json"
            }
            response = requests.post(
                f'{self.reject_url}?{querystr}',
                headers=headers,
                data=body.encode('utf-8'),
                timeout=20
            )
            if response.status_code == 200:
                res = response.json()
                et_log.info(f"现代拒识接口响应结果：{res}")
                reject = res.get('data', {}).get('reject', True)  # 如果路径不存在，默认返回True
                et_log.info(f"获取到的reject值: {reject}")
            else:
                raise Exception(f"现代拒识接口调用失败，HTTP状态码: {response.status_code}")
        except Exception as e:
            et_log.error(f"现代拒识接口调用失败：{e}")

    def Hyundai_reject_multi(self, prompt):
        """
        现代拒识
        """
        reject = True
        try:
            method = "POST"
            timestamp = int(time.time())
            random_integer = random.randint(0, 65535)
            chat_id = uuid4().hex
            request_id = uuid4().hex
            querystr = f"_timestamp={timestamp}&_nonce={random_integer}&chat_id={chat_id}&request_id={request_id}"
            params = {
                "query": prompt
            }
            body = json.dumps(params, ensure_ascii=False)
            sign = self.generate_signature(self.sk, method, querystr, body)
            et_log.info(f"签名生成成功{sign}")
            headers = {
                "X-Signature": f'{self.ak}:{sign}',
                "Device-Id": f"{self.device_id}",
                "content-type": "application/json"
            }
            response = requests.post(
                f'{self.reject_url}?{querystr}',
                headers=headers,
                data=body.encode('utf-8'),
                timeout=20
            )
            if response.status_code == 200:
                res = response.json()
                et_log.info(f"现代拒识接口响应结果：{res}")
                reject = res.get('data', {}).get('reject', True)  # 如果路径不存在，默认返回True
                et_log.info(f"获取到的reject值: {reject}")
            else:
                raise Exception(f"现代拒识接口调用失败，HTTP状态码: {response.status_code}")
        except Exception as e:
            et_log.error(f"现代拒识接口调用失败：{e}")

    def Hyundai_intent(self):
        """
        现代意图
        """
        intent_type = ''
        origin_intent_type = ''
        question_id = ''
        intent_time = 0
        intent_log_id = ''
        try:
            et_log.info(f"Hyundai:intention task_id:{self.task_id} result_id:{self.result_id} conf_id:{self.conf_id}")
            method = "POST"
            timestamp = int(time.time())
            random_integer = random.randint(0, 65535)
            chat_id = uuid4().hex
            request_id = uuid4().hex
            querystr = f"_timestamp={timestamp}&_nonce={random_integer}&chat_id={chat_id}&request_id={request_id}"
            params = {
                "query": self.prompt
            }
            body = json.dumps(params, ensure_ascii=False)
            sign = self.generate_signature(self.sk, method, querystr, body)
            et_log.info(f"签名生成成功{sign}")
            headers = {
                "X-Signature": f'{self.ak}:{sign}',
                "Device-Id": f"{self.device_id}",
                "content-type": "application/json"
            }
            response = requests.post(
                f'{self.intent_url}?{querystr}',
                headers=headers,
                data=body.encode('utf-8'),
                timeout=20
            )
            if response.status_code == 200:
                search_results = response.text
                et_log.info(f"现代意图响应结果：{search_results}")
                data = json.loads(search_results)
                intent_type = data.get('data', {}).get('intent_type', '')
                origin_intent_type = data.get('data', {}).get('origin_intent_type', '')
                question_id = data.get('data', {}).get('question_id')
                intent_time = data.get('data', {}).get('debug_info', {}).get('intent_time', '')
                intent_log_id = data.get('data', {}).get('debug_info', {}).get('log_id', '')
                et_log.info(f"现代意图获取结果--intent_type：{intent_type},origin_intent_type：{origin_intent_type},"
                            f"question_id：{question_id},intent_time:{intent_time},intent_log_id:{intent_log_id}")
                return intent_type, origin_intent_type, question_id, intent_time, intent_log_id
        except Exception as e:
            et_log.info(f"现代意图调用失败:{e}")
            return intent_type, origin_intent_type, question_id, intent_time, intent_log_id

    def Hyundai_reject_and_intent(self):
        try:
            reject = self.Hyundai_reject()
            if not reject:
                intent_type, origin_intent_type, question_id, intent_time, intent_log_id = self.Hyundai_intent()
                timestamp = int(time.time())
                return reject, intent_type, origin_intent_type, question_id, intent_time, intent_log_id
        except Exception as e:
            et_log.info(f"现代拒识意图调用失败:{e}")

    def Hyundai_intent_multi(self, prompt):
        """
        现代意图
        """
        intent_type = ''
        origin_intent_type = ''
        question_id = ''
        intent_time = 0
        intent_log_id = ''
        try:
            et_log.info(f"Hyundai:intention task_id:{self.task_id} result_id:{self.result_id} conf_id:{self.conf_id}")
            method = "POST"
            timestamp = int(time.time())
            random_integer = random.randint(0, 65535)
            chat_id = uuid4().hex
            request_id = uuid4().hex
            querystr = f"_timestamp={timestamp}&_nonce={random_integer}&chat_id={chat_id}&request_id={request_id}"
            params = {
                "query": prompt
            }
            body = json.dumps(params, ensure_ascii=False)
            sign = self.generate_signature(self.sk, method, querystr, body)
            et_log.info(f"签名生成成功{sign}")
            headers = {
                "X-Signature": f'{self.ak}:{sign}',
                "Device-Id": f"{self.device_id}",
                "content-type": "application/json"
            }
            response = requests.post(
                f'{self.intent_url}?{querystr}',
                headers=headers,
                data=body.encode('utf-8'),
                timeout=20
            )
            if response.status_code == 200:
                search_results = response.text
                et_log.info(f"现代意图响应结果：{search_results}")
                data = json.loads(search_results)
                intent_type = data.get('data', {}).get('intent_type', '')
                origin_intent_type = data.get('data', {}).get('origin_intent_type', '')
                question_id = data.get('data', {}).get('question_id')
                intent_time = data.get('data', {}).get('debug_info', {}).get('intent_time', '')
                intent_log_id = data.get('data', {}).get('debug_info', {}).get('log_id', '')
                et_log.info(f"现代意图获取结果--intent_type：{intent_type},origin_intent_type：{origin_intent_type},"
                            f"question_id：{question_id},intent_time:{intent_time},intent_log_id:{intent_log_id}")
                return intent_type, origin_intent_type, question_id, intent_time, intent_log_id
        except Exception as e:
            et_log.info(f"现代意图调用失败:{e}")
            return intent_type, origin_intent_type, question_id, intent_time, intent_log_id

    def Hyundai_reject_and_intent_multi(self, prompt):
        try:
            reject = self.Hyundai_reject_multi(prompt)
            if not reject:
                intent_type, origin_intent_type, question_id, intent_time, intent_log_id = self.Hyundai_intent_multi(
                    prompt)
                timestamp = int(time.time())
                return reject, intent_type, origin_intent_type, question_id, intent_time, intent_log_id
        except Exception as e:
            et_log.info(f"现代拒识意图调用失败:{e}")

    def Hyundai_Plugin_text_to_scene(self):
        """
        现代文生场景
        """
        try:
            flag = False
            method = "POST"
            first_res_time = 0
            consume_time = 0
            intent_time = 0
            total_time = 0
            log_id = ""
            intent_log_id =""
            reject, intent_type, origin_intent_type, question_id, intent_time, intent_log_id = self.Hyundai_reject_and_intent()
            if intent_type == "text_to_scene" and not reject and question_id:
                plugin_template["query"] = self.prompt
                plugin_template["chat_id"] = uuid4().hex
                plugin_template["question_id"] = question_id
                plugin_template["intent_type"] = intent_type
                timestamp = int(time.time())
                random_integer = random.randint(0, 65535)
                question_id = uuid4().hex
                chat_id = uuid4().hex
                querystr = f"_timestamp={timestamp}&_nonce={random_integer}&chat_id={chat_id}&question_id={question_id}"
                body = json.dumps(plugin_template, ensure_ascii=False)
                sign = self.generate_signature(self.sk, method, querystr, body)
                headers = {
                    "X-Signature": f'{self.ak}:{sign}',
                    "Device-Id": f"{self.device_id}",
                    "Vehicle-Type": f"{self.vehicle_type}",
                    "content-type": "application/json;charset=utf-8"
                }
                start_time = time.time()
                response = requests.post(
                    f'{self.plugin_url}?{querystr}',
                    headers=headers,
                    data=body.encode('utf-8'),
                    stream=True  # 启用流式响应以处理SSE格式数据
                )
                if response.status_code == 200:
                    full_content = ""
                    for line in response.iter_lines():
                        if line:
                            decoded_line = line.decode('utf-8')
                            et_log.info(f"decoded_line:{decoded_line}")
                            if decoded_line.startswith('data:'):
                                event_data = json.loads(decoded_line[5:])
                                et_log.info(f"event_data:{event_data}"),
                                if 'observation' in str(event_data):
                                    title = event_data.get('title','')
                                    chunk1 = event_data.get('observation','')
                                    chunk = f"observation:{chunk1},\n\ntitle:{title}\n\n"
                                    full_content += chunk
                                    print(chunk, end="", flush=True)
                                if "debug_info" in event_data:
                                    debug_info = event_data.get('debug_info')
                                    if debug_info:
                                        plugin_info = event_data['debug_info']['plugin_info']
                                        chunk2 = f"\n\nplugin_info:{plugin_info}"
                                        full_content += chunk2
                                        log_id = event_data.get('debug_info', {}).get('log_id', '')
                                        total_time = debug_info.get('total_time')
                                        consume_time = round(float(intent_time + total_time) / 1000, 2)
                                        first_res_time = consume_time
                                        et_log.info(
                                            f"first_res_time:{first_res_time},intent_time:{intent_time},consume_time:{consume_time}")
                remark = f"plugin_log_id:{log_id}|intent_time:{intent_time}|intent_log_id:{intent_log_id}"
                item_result = self.build_result_item(str(self.item._id), self.task_id, self.result_id, "",
                                                     intent_type,
                                                     (self.item).expected_answer, full_content,
                                                     first_res_time, consume_time, log_id, "", remark,
                                                     self.do_user, self.oem_codes, self.conf_id)

                item_result["result_answer"] = 0  # 回答结果
                if full_content:
                    item_result["result_answer"] = 1
                    item_result["result_final"] = item_result["result_answer"]  # 最终结果
                else:
                    item_result["result_final"] = 0
                result_category = 0
                if self.item.expected_category and len(self.item.expected_category) > 0 and str(
                        self.item.expected_category) == str(intent_type):
                    result_category = 1
                item_result["result_category"] = result_category
                et_log.info(f"Pentium_trip_plan_item_result:{item_result}")
                item_result_id = TestItemResultService.insert_one(item_result)
                et_log.info(f"Pentium_trip_plan:insert_one item_result_id:{item_result_id}")
            return item_result_id
        except Exception as e:
            et_log.error(f"现代座舱插件请求失败：{e}")

    def Hyundai_News(self):
        """现代AI新闻"""
        try:
            flag = False
            method = "POST"
            first_token_time = 0
            intent_time = 0
            total_time = 0
            first_res_time = 0
            consume_time = 0
            intent_log_id = ""
            plugin_log_id = ""
            plugin_time = 0
            plugin_time_res = ""
            vedio_result = []
            vedio_title = ""
            titles = []
            origin_intent_type = ""
            error_message = ''
            full_content = ""
            remark = ""
            reject, intent_type, origin_intent_type, question_id, intent_time, intent_log_id = self.Hyundai_reject_and_intent()
            if str(intent_type) in {"news", "douyin_video"} and not reject and question_id:
                plugin_template["query"] = self.prompt
                plugin_template["chat_id"] = uuid4().hex
                plugin_template["question_id"] = question_id
                plugin_template["intent_type"] = intent_type
                plugin_template["intent_log_id"] = intent_log_id
                plugin_template["limit"] = 10
                timestamp = int(time.time())
                random_integer = random.randint(0, 65535)
                question_id = uuid4().hex
                chat_id = uuid4().hex
                querystr = f"_timestamp={timestamp}&_nonce={random_integer}&chat_id={chat_id}&question_id={question_id}"
                body = json.dumps(plugin_template, ensure_ascii=False)
                sign = self.generate_signature(self.sk, method, querystr, body)
                headers = {
                    "X-Signature": f'{self.ak}:{sign}',
                    "Device-Id": f"{self.device_id}",
                    "Vehicle-Type": f"{self.vehicle_type}",
                    "content-type": "application/json;charset=utf-8"
                }
                response = requests.post(
                    f'{self.plugin_url}?{querystr}',
                    headers=headers,
                    data=body.encode('utf-8'),
                    stream=True  # 启用流式响应以处理SSE格式数据
                )
                if response.status_code == 200:
                    has_error = False
                    for line in response.iter_lines():
                        if line:
                            decoded_line = line.decode('utf-8')
                            et_log.info(f"decoded_line:{decoded_line}")
                            if decoded_line.startswith('data:'):
                                event_data = json.loads(decoded_line[5:])
                                if 'error_message' in str(event_data):
                                    error_message = event_data['error_message']
                                    if error_message:
                                        total_time = event_data.get("debug_info", {}).get('total_time')
                                        first_res_time = round(float(intent_time + total_time) / 1000, 2)
                                        consume_time = round(float(intent_time + total_time) / 1000, 2)
                                        tts_content = error_message
                                        has_error = True
                                        break
                                if not has_error and "content" in event_data:
                                    chunk = event_data['content']
                                    full_content += chunk
                                    print(chunk, end="", flush=True)
                                if not has_error and "debug_info" in event_data:
                                    debug_info = event_data.get('debug_info')
                                    if debug_info:
                                        plugin_log_id = event_data.get('debug_info', {}).get('log_id', '')
                                        total_time = debug_info.get('total_time')
                                        first_token_time = debug_info.get('first_token_time')
                                        first_res_time = round(float(first_token_time + intent_time) / 1000, 2)
                                        consume_time = round(float(intent_time + total_time) / 1000, 2)
                                        if "plugin_time" in event_data:
                                            plugin_time = event_data.get("debug_info", {}).get("plugin_info", {}).get(
                                                "function_call", {}).get("plugin_time", 0)
                                            if plugin_time:
                                                print("plugin_time:", plugin_time)
                                                plugin_time_res = f"{float(plugin_time) / 1000}s"
                                    if "observation" in event_data:
                                        observation = event_data["observation"]
                                        if isinstance(observation, str):
                                            observation = json.loads(observation)
                                        if "list" in observation:
                                            for item in observation["list"]:
                                                video_info = {
                                                    "title": item.get("title", ""),
                                                    "detail_url": item.get("detail_url", ""),
                                                    "cover_url": item.get("cover_url", "")
                                                }
                                                vedio_result.append(video_info)
                                            if vedio_result:
                                                titles = [f"title{i + 1}: {item['title']}" for i, item in
                                                          enumerate(vedio_result)]
                                                vedio_title = "，".join(titles)

                    if vedio_title:
                        full_content = f"{full_content}\n\nkeywords:\n\n{vedio_title}"
                    if intent_log_id and plugin_log_id and intent_time and origin_intent_type:
                        remark = f"intent_log_id:{intent_log_id}|plugin_log_id:{plugin_log_id}|intent_time:{intent_time}|origin_intent_type{origin_intent_type}"
            item_result = self.build_result_media_item(str(self.item._id), self.task_id, self.result_id, "",
                                                       intent_type,
                                                       (self.item).expected_answer, full_content,
                                                       first_res_time, consume_time, plugin_log_id, "", remark, "",
                                                       str(vedio_result),
                                                       self.do_user, self.oem_codes, self.conf_id)

            item_result["result_answer"] = 0  # 回答结果
            if full_content and not error_message:
                item_result["result_answer"] = 1
                item_result["result_final"] = item_result["result_answer"]  # 最终结果
            else:
                item_result["result_final"] = 0
            result_category = 0
            if self.item.expected_category and len(self.item.expected_category) > 0 and str(
                    self.item.expected_category) == str(intent_type):
                result_category = 1
            item_result["result_category"] = result_category
            et_log.info(f"Pentium_trip_plan_item_result:{item_result}")
            item_result_id = TestItemResultService.insert_one(item_result)
            et_log.info(f"Pentium_trip_plan:insert_one item_result_id:{item_result_id}")
            return item_result_id
        except Exception as e:
            et_log.error(f"现代座舱插件请求失败：{e}")

    def Hyundai_Plugin_FC(self):
        """
        现代车控
        """
        try:
            flag = False
            method = "POST"
            first_token_time = 0
            start_time = 0
            first_info_time = 0
            intent_time = 0
            total_time = 0
            plugin_cmd = ""
            tts_content = ""
            plugin_time = 0
            first_res_time = 0
            consume_time = 0
            log_id = ""
            plugin_time_res = ""
            timestamp = int(time.time())
            model_info = ""
            intent_log_id = ""
            plugin_cmd = ""
            reject, intent_type, origin_intent_type, question_id, intent_time, intent_log_id = self.Hyundai_reject_and_intent()
            if intent_type == "vehicle_control" and not reject and question_id:
                plugin_template["query"] = self.prompt
                plugin_template["chat_id"] = uuid4().hex
                plugin_template["question_id"] = question_id
                plugin_template["intent_type"] = intent_type
                plugin_template["intent_log_id"] = intent_log_id
                et_log.info(f"query_body:{plugin_template}")
                random_integer = random.randint(0, 65535)
                question_id = uuid4().hex
                chat_id = uuid4().hex
                querystr = f"_timestamp={timestamp}&_nonce={random_integer}&chat_id={chat_id}&question_id={question_id}"
                body = json.dumps(plugin_template, ensure_ascii=False)
                sign = self.generate_signature(self.sk, method, querystr, body)
                headers = {
                    "X-Signature": f'{self.ak}:{sign}',
                    "Device-Id": f"{self.device_id}",
                    "Vehicle-Type": f"{self.vehicle_type}",
                    "content-type": "application/json;charset=utf-8"
                }
                start_time = time.time()
                response = requests.post(
                    f'{self.plugin_url}?{querystr}',
                    headers=headers,
                    data=body.encode('utf-8'),
                    stream=True,  # 启用流式响应以处理SSE格式数据
                )
                if response.status_code == 200:
                    has_error = False
                    for line in response.iter_lines():
                        if not line:
                            et_log.debug("Received empty line, continuing...")
                            continue
                        decoded_line = line.decode('utf-8')
                        if decoded_line.startswith('data:'):
                            et_log.info(f"decoded_line:{decoded_line}")

                            event_data = json.loads(decoded_line[5:])
                            et_log.info(f"event_data:{event_data}")
                            if 'error_message' in str(event_data):
                                error_message = event_data['error_message']
                                if error_message:
                                    total_time = event_data.get("debug_info", {}).get('total_time')
                                    first_res_time = round(float(intent_time + total_time) / 1000, 2)
                                    consume_time = round(float(intent_time + total_time) / 1000, 2)
                                    tts_content = error_message
                                    has_error = True
                                    break
                            if not has_error and "content" in event_data:
                                content = event_data['content']
                                tts_content += content
                                tts_content = tts_content.replace(" ", "")
                            if not has_error and "debug_info" in event_data:
                                debug_info = event_data.get('debug_info')
                                if debug_info:
                                    log_id = event_data.get('debug_info', {}).get('log_id', '')
                                    model_info = event_data.get('debug_info', {}).get('model_info', '')
                                    plugin_info = event_data.get('debug_info', {}).get('plugin_info', '')
                                    plugin_time = event_data.get("debug_info", {}).get("plugin_info", {}).get(
                                        "function_call", {}).get("plugin_time")
                                    if plugin_time is not None:
                                        print("plugin_time:", plugin_time)
                                    plugin_time_res = f"{float(plugin_time) / 1000}s"
                                    plugin_cmd += f"model_info:\n{model_info}\n\nplugin_info:\n{plugin_info}"
                                    total_time = debug_info.get('total_time')
                                    first_token_time = debug_info.get('first_token_time')
                                    first_res_time = round(float(first_token_time + intent_time) / 1000, 2)
                                    consume_time = round(float(intent_time + total_time) / 1000, 2)
                                    et_log.info(
                                        f"first_token_time:{first_token_time},intent_time:{intent_time},total_time{total_time}")
                                    tts_content=f"tts_content:{tts_content}\n\nFC回复：{plugin_cmd}"
            remark = f"plugin_log_id:{log_id}|intent_time:{intent_time}|intent_log_id:{intent_log_id}|plugin_time:{plugin_time_res}"
            item_result = self.build_FC_result_item(str(self.item._id), self.task_id, self.result_id, "",
                                                    intent_type,
                                                    (self.item).expected_answer, tts_content,
                                                    first_res_time, consume_time, log_id, "", plugin_cmd, remark,
                                                    self.do_user, self.oem_codes, self.conf_id, self.parent_id)
            item_result["result_answer"] = 0  # 回答结果
            if plugin_cmd and not error_message:
                item_result["result_answer"] = 1
                item_result["result_final"] = item_result["result_answer"]  # 最终结果
            else:
                item_result["result_final"] = 0
            result_category = 0
            if self.item.expected_category and len(self.item.expected_category) > 0 and str(
                    self.item.expected_category) == str(intent_type):
                result_category = 1
            item_result["result_category"] = result_category
            et_log.info(f"Hyundai_vehicle_control_FC_item_result:{item_result}")
            item_result_id = TestItemResultService.insert_one(item_result)
            et_log.info(f"Hyundai_vehicle_control_FC:insert_one item_result_id:{item_result_id}")
            return item_result_id
        except Exception as e:
            et_log.error(f"现代座舱插件请求失败：{e}")

    def Hyundai_Plugin_media(self):
        """
        AI多媒体
        """
        flag = False
        method = "POST"
        first_res_time = 0
        consume_time = 0
        log_id = 0
        first_token_time = 0
        intent_time = 0
        total_time = 0
        plugin_time = 0
        plugin_time_res = ""
        remark = ""
        intent_log_id = ""
        origin_intent_type = ""

        try:
            plugin_template["chat_id"] = uuid4().hex
            origin_data = [(self.item).to_json_str()] + self.children
            origin_tree_data = self.convert_to_tree(origin_data)
            et_log.info(f"origin_tree_data:{origin_tree_data}")
            prompts = self.extract_questions(origin_tree_data[0])
            et_log.info(f"prompts____________________________-:{prompts}")
            chat_id = uuid4().hex
            history = []
            for i in range(len(prompts)):
                full_content = ""
                vedio_result = []
                vedio_title = ""
                reject, intent_type, origin_intent_type, question_id, intent_time, intent_log_id = self.Hyundai_reject_and_intent_multi(
                    prompt=prompts[i])
                et_log.info(f"AI多媒体query:{prompts[i]}对应意图为{intent_type}")
                if i > 0:  # 第一轮没有历史，从第二轮开始添加
                    history.append({
                        "q": prompts[i - 1],  # 上一轮的query
                        "a": full_content_prev  # 上一轮的回答
                    })
                    plugin_template["history"] = history
                if str(intent_type) in {"music_info", "douyin_video"} and not reject and question_id:
                    plugin_template["query"] = prompts[i]
                    plugin_template["question_id"] = question_id
                    plugin_template["intent_type"] = intent_type
                    plugin_template["intent_log_id"] = intent_log_id
                    plugin_template["chat_id"] = chat_id
                    timestamp = int(time.time())
                    random_integer = random.randint(0, 65535)
                    question_id = uuid4().hex
                    chat_id = uuid4().hex
                    querystr = f"_timestamp={timestamp}&_nonce={random_integer}&chat_id={chat_id}&question_id={question_id}"
                    body = json.dumps(plugin_template, ensure_ascii=False)
                    sign = self.generate_signature(self.sk, method, querystr, body)
                    headers = {
                        "X-Signature": f'{self.ak}:{sign}',
                        "Device-Id": f"{self.device_id}",
                        "Vehicle-Type": f"{self.vehicle_type}",
                        "content-type": "application/json;charset=utf-8"
                    }
                    response = requests.post(
                        f'{self.plugin_url}?{querystr}',
                        headers=headers,
                        data=body.encode('utf-8'),
                        stream=True  # 启用流式响应以处理SSE格式数据
                    )
                    if response.status_code == 200:
                        has_error = False
                        for line in response.iter_lines():
                            if line:
                                decoded_line = line.decode('utf-8')
                                et_log.info(f"decoded_line:{decoded_line}")
                                if decoded_line.startswith('data:'):
                                    event_data = json.loads(decoded_line[5:])
                                    error_code = event_data['error_code']
                                    content = event_data.get('content', '')
                                    error_message = event_data['error_message']
                                    if error_message:
                                        total_time = event_data.get("debug_info", {}).get('total_time')
                                        first_res_time = round(float(intent_time + total_time) / 1000, 2)
                                        consume_time = round(float(intent_time + total_time) / 1000, 2)
                                        full_content = error_message
                                        has_error = True
                                        break
                                    if not has_error and "content" in event_data:
                                        content = event_data['content']
                                        full_content += content
                                    if "debug_info" in event_data and not has_error:
                                        debug_info = event_data.get('debug_info')
                                        if debug_info:
                                            log_id = event_data.get('debug_info', {}).get('log_id', '')
                                            total_time = debug_info.get('total_time')
                                            first_token_time = debug_info.get('first_token_time')
                                            et_log.info(
                                                f"first_token_time:{first_token_time},intent_time:{intent_time},total_time{total_time}")
                                            first_res_time = round(float(first_token_time + intent_time) / 1000, 2)
                                            consume_time = round(float(intent_time + total_time) / 1000, 2)
                                            intent_time = f"{float(intent_time) / 1000}s"
                                            if "plugin_time" in event_data:
                                                plugin_time = event_data.get("debug_info", {}).get("plugin_info",
                                                                                                   {}).get(
                                                    "function_call", {}).get("plugin_time", "")
                                                if plugin_time:
                                                    print("plugin_time:", plugin_time)
                                                    plugin_time_res = f"{float(plugin_time) / 1000}s"
                                    if "observation" in event_data and not has_error:
                                        observation = event_data["observation"]
                                        if isinstance(observation, str):
                                            observation = json.loads(observation)
                                        if "list" in observation:
                                            for item in observation["list"]:
                                                video_info = {
                                                    "title": item.get("title", ""),
                                                    "detail_url": item.get("detail_url", ""),
                                                    "cover_url": item.get("cover_url", "")
                                                }
                                                vedio_result.append(video_info)
                                            if vedio_result:
                                                titles = [f"title{i + 1}: {item['title']}" for i, item in
                                                          enumerate(vedio_result)]
                                                vedio_title = "，".join(titles)

                        if vedio_title:
                            full_content = f"{full_content}\nkeywords:\n{vedio_title}"
                        full_content_prev = full_content
                        if log_id and intent_time and intent_log_id and plugin_time_res:
                            remark = f"plugin_log_id:{log_id}|intent_time:{intent_time}|intent_log_id:{intent_log_id}|plugin_time:{plugin_time_res}|origin_intent_type:{origin_intent_type}"

                if i == 0:
                    itemid = self.item._id
                    parent_id = self.parent_id
                else:
                    itemid = origin_data[i]["_id"]
                    parent_id = item_result_id
                if log_id and intent_time and intent_log_id and plugin_time_res:
                    remark = f"plugin_log_id:{log_id}|intent_time:{intent_time}|intent_log_id:{intent_log_id}|plugin_time:{plugin_time_res}|origin_intent_type:{origin_intent_type}"

                item_result = self.build_result_media_item(str(itemid), self.task_id, self.result_id, "",
                                                           intent_type,
                                                           (self.item).expected_answer, full_content,
                                                           first_res_time, consume_time, log_id, "", remark, parent_id,
                                                           str(vedio_result),
                                                           self.do_user, self.oem_codes, self.conf_id)

                item_result["result_answer"] = 0  # 回答结果
                if full_content and not error_message:
                    item_result["result_answer"] = 1
                    item_result["result_final"] = item_result["result_answer"]  # 最终结果
                else:
                    item_result["result_final"] = 0
                result_category = 0
                if origin_data[i]["expected_category"] and len(origin_data[i]["expected_category"]) > 0 and str(
                        origin_data[i]["expected_category"]) == str(intent_type):
                    result_category = 1
                item_result["result_category"] = result_category
                et_log.info(f"Hyundai_media_item_result:{item_result}")
                item_result_id = TestItemResultService.insert_one(item_result)
                et_log.info(f"Hyundai_media:insert_one item_result_id:{item_result_id}")
            return item_result_id
        except Exception as e:
            et_log.error(f"现代座舱插件请求失败：{e}")

    def Hyundai_Sing_Draw(self):
        """AI画布"""
        try:
            flag = False
            method = "POST"
            start_time = None
            first_info_time = None
            end_time = None
            first_res_time = 0
            consume_time = 0
            res = ""
            timestamp = int(time.time())
            chat_id = uuid4().hex
            question_id = uuid4().hex
            random_integer = random.randint(0, 65535)
            param = {
                "songName": self.prompt,
                "channelId": "1664546346600734728"
            }
            querystr = f"vehicle_id={self.vehicle_id}&chat_id={chat_id}&question_id={question_id}&_nonce={random_integer}&_timestamp={timestamp}"
            body = json.dumps(param, ensure_ascii=False)
            sign = self.generate_signature(self.sk, method, querystr, body)
            headers = {
                "X-Signature": f'{self.ak}:{sign}',
                "content-type": "application/json;charset=utf-8"
            }
            start_time = time.time()
            response = requests.post(
                f'{self.sing_draw_url}?{querystr}',
                headers=headers,
                data=body.encode('utf-8')
            )
            et_log.info(f"start_time{start_time}")
            if response.status_code == 200:
                data = json.loads(response.text)
                if data:
                    urls = [item['url'] for item in data.get('data', [])]
                    res = ", ".join([f"url{i + 1}:{url}" for i, url in enumerate(urls)])
                    et_log.info(f"音乐画布结果{res}")
                    if not flag:
                        first_info_time = time.time()
                        et_log.info(f"first_info_time{first_info_time}")
                        flag = True
            end_time = time.time()
            if first_info_time:
                first_res_time = float(first_info_time - start_time)
            if end_time:
                consume_time = float(end_time - start_time)
            et_log.info(f"first_res_time{first_res_time},consume_time:{consume_time}")
            item_result = self.build_result_item(str(self.item._id), self.task_id, self.result_id, "",
                                                 "壁纸生成",
                                                 (self.item).expected_answer, res,
                                                 first_res_time, consume_time, "", "",
                                                 self.do_user, self.oem_codes, self.conf_id)

            item_result["result_answer"] = 0  # 回答结果
            if res:
                item_result["result_answer"] = 1
                item_result["result_final"] = item_result["result_answer"]  # 最终结果
            else:
                item_result["result_final"] = 0
            et_log.info(f"Pentium_trip_plan_item_result:{item_result}")
            item_result_id = TestItemResultService.insert_one(item_result)
            et_log.info(f"Pentium_trip_plan:insert_one item_result_id:{item_result_id}")
            time.sleep(1)
        except Exception as e:
            et_log.error(f"API调用失败: {e}")
            return None, None

    def generate_signature(self, sk: str, method: str, querystr: str, body: Optional[str]) -> str:
        """生成请求签名"""
        try:
            # 对查询参数按字典序排序
            a = querystr.split("&")
            a.sort()
            sortedquerystr = "&".join(a)

            # 构建待签名字符串
            strtosign = method + "\n" + sortedquerystr + "\n"
            if body is not None and len(body) > 0:
                m = hashlib.md5()
                m.update(body.encode("utf8"))
                strtosign += m.hexdigest() + "\n"

            # 计算HMAC-SHA256签名
            h = hmac.new(sk.encode("utf8"), strtosign.encode("utf8"), sha256).digest()
            signature = base64.b64encode(h).decode()
            et_log.info("Generated signature successfully")
            return signature
        except Exception as e:
            error_msg = f"Signature generation failed: {e}"
            et_log.error(error_msg, exc_info=True)

    def build_FC_result_item(self, item_id, task_id, task_result_id, expected_category, actual_category,
                             expected_answer, actual_answer,
                             first_res_time, qa_use_time, log_id, question_id, plugin_cmd, remark, do_user=SYS_USER,
                             oem_codes="", conf_id="", parent_id=""):
        et_log.info(
            f"ByteDanceAdapter:build_result_item item_id:{item_id} task_id:{task_id} task_result_id:{task_result_id} "
            f"conf_id:{conf_id} expected_category:{expected_category} actual_category:{actual_category} "
            f"expected_answer:{expected_answer} actual_answer:{actual_answer} "
            f"first_res_time:{first_res_time} qa_use_time:{qa_use_time} log_id:{log_id} question_id:{question_id}")
        if not CTX_OEM_CODES.get() and len(oem_codes) > 0:
            CTX_OEM_CODES.set(oem_codes)
        if not CTX_USER_ID.get():
            CTX_OEM_CODES.set(do_user)
        return {"parent_id": parent_id, "data_set_item_id": str(item_id), "task_id": task_id,
                "task_result_id": str(task_result_id), "actual_task": "", "actual_category": actual_category,
                "actual_answer": actual_answer, "result_answer": 0, "answer_score": 0,
                "qa_recall": 0, "qa_use_time": qa_use_time, "recall_id": log_id,
                "remark": f"remark:{remark}", "first_res_time": first_res_time,
                "do_user": do_user, "re_interval_time": 0, "is_websearch": 0, "re_cmd": plugin_cmd,
                "result_category": 0}

    def build_result_item(self, item_id, task_id, task_result_id, expected_category, actual_category, expected_answer,
                          actual_answer,
                          first_res_time, qa_use_time, log_id, question_id, remark, do_user=SYS_USER, oem_codes="",
                          conf_id=""):
        et_log.info(
            f"ByteDanceAdapter:build_result_item item_id:{item_id} task_id:{task_id} task_result_id:{task_result_id} "
            f"conf_id:{conf_id} expected_category:{expected_category} actual_category:{actual_category} "
            f"expected_answer:{expected_answer} actual_answer:{actual_answer} "
            f"first_res_time:{first_res_time} qa_use_time:{qa_use_time} log_id:{log_id} question_id:{question_id}")
        if not CTX_OEM_CODES.get() and len(oem_codes) > 0:
            CTX_OEM_CODES.set(oem_codes)
        if not CTX_USER_ID.get():
            CTX_OEM_CODES.set(do_user)
        return {"parent_id": "", "data_set_item_id": str(item_id), "task_id": task_id,
                "task_result_id": str(task_result_id), "actual_task": "", "actual_category": actual_category,
                "actual_answer": actual_answer, "result_answer": 0, "answer_score": 0,
                "qa_recall": 0, "qa_use_time": qa_use_time, "recall_id": log_id,
                "remark": remark, "first_res_time": first_res_time,
                "do_user": do_user, "re_interval_time": 0, "is_websearch": 0, "result_category": 0}

    def build_result_media_item(self, item_id, task_id, task_result_id, expected_category, actual_category,
                                expected_answer, actual_answer,
                                first_res_time, qa_use_time, log_id, question_id, remark, parent_id, play_url,
                                do_user=SYS_USER, oem_codes="", conf_id=""):
        et_log.info(
            f"ByteDanceAdapter:build_result_item item_id:{item_id} parent_id:{parent_id} task_id:{task_id} task_result_id:{task_result_id} "
            f"conf_id:{conf_id} expected_category:{expected_category} actual_category:{actual_category} "
            f"expected_answer:{expected_answer} actual_answer:{actual_answer} "
            f"first_res_time:{first_res_time} qa_use_time:{qa_use_time} log_id:{log_id} question_id:{question_id}")
        if not CTX_OEM_CODES.get() and len(oem_codes) > 0:
            CTX_OEM_CODES.set(oem_codes)
        if not CTX_USER_ID.get():
            CTX_OEM_CODES.set(do_user)
        return {"parent_id": parent_id, "data_set_item_id": str(item_id), "task_id": task_id,
                "task_result_id": str(task_result_id), "actual_task": "", "actual_category": actual_category,
                "actual_answer": actual_answer, "result_answer": 0, "answer_score": 0,
                "qa_recall": 0, "qa_use_time": qa_use_time, "recall_id": log_id,
                "remark": remark, "first_res_time": first_res_time,
                "do_user": do_user, "re_interval_time": 0, "is_websearch": 0, "result_category": 0, "re_url": play_url}

    def convert_to_tree(self, data):
        # 创建一个字典，用于根据 _id 查找每个节点
        id_map = {item['_id']: item for item in data}

        # 创建一个空的列表，用于存储根节点（parent_id 为空的节点）
        root_nodes = []

        # 遍历所有数据，设置每个节点的子节点
        for item in data:
            # 如果 parent_id 为空，则为根节点，加入 root_nodes
            if not item['parent_id']:
                root_nodes.append(item)
            else:
                # 否则，把该节点添加到父节点的 children 列表中
                parent = id_map.get(item['parent_id'])
                if parent:
                    # 确保父节点存在 children 列表
                    if 'children' not in parent:
                        parent['children'] = []
                    parent['children'].append(item)
                else:
                    et_log.error(f"Parent not found for item: {item['_id']} with parent_id: {item['parent_id']}")

        return root_nodes

    def extract_questions(self, data, result=None):
        if result is None:
            result = []  # 初始化空列表

        # 如果当前数据包含"question"字段，将其值添加到结果列表中
        if "question" in data:
            result.append(data["question"])

        # 如果当前数据包含"children"字段，递归遍历每一个子项
        if "children" in data:
            for child in data["children"]:
                self.extract_questions(child, result)

        return result




