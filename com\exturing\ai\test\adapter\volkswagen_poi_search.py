import base64
import hashlib
import hmac
import json
import random
import re
import time
from hashlib import sha256
from typing import Optional
from uuid import uuid4
import requests
from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES, CTX_USER_ID, SYS_USER
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.test_config import TestConfigInfoModel
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService


class VolkswagenPoiSearch:
    """POI搜索"""

    def __init__(self, config_info: TestConfigInfoModel, item: EtDataSetItem, task_id, result_id, conf_id, do_user,
                 oem_codes=""):
        # 云端运行
        model_params_str = config_info.model_params
        model_params_dict = json.loads(model_params_str)
        self.device_id = model_params_dict.get("Device_Id")
        self.vehicle_type = model_params_dict.get("Vehicle_Type")
        self.plugin_stream_url = model_params_dict.get("plugin_stream_url")
        self.ak = config_info.model_ak
        self.sk = config_info.model_sk
        self.intent_url = config_info.model_url
        self.prompt = item.question
        self.task_id = task_id
        self.result_id = result_id
        self.conf_id = conf_id
        self.item = item
        self.do_user = do_user
        self.oem_codes = oem_codes


    def intent_reject(self):
        """
        大众适配=意图识别
        """
        et_log.info(
            f"Volkswagen_context:intention task_id:{self.task_id} result_id:{self.result_id} conf_id:{self.conf_id}")
        method = "POST"
        timestamp = int(time.time())
        random_integer = random.randint(0, 65535)
        chat_id = uuid4().hex
        question_id = uuid4().hex
        querystr = f"_timestamp={timestamp}&_nonce={random_integer}&chat_id={chat_id}&question_id={question_id}"
        intent_request_body = {
            "query": self.prompt,
            "history": [
                {
                    "q": "eu exercitation consectetur enim quis",
                    "a": "enim mollit sit adipisicing"
                },
                {
                    "q": "ad consectetur irure qui",
                    "a": "aute exercitation ut"
                }
            ],
            "car_info": {
                "car_heading": 150,
                "current_lng_lat": {
                    "longitude": 116.460988,
                    "latitude": 40.006919
                },
                "vehicle_name": "掀背车",
                "sound_zone": "consequat culpa veniam ullamco deserunt",
                "video_name": "雪豹",
                "video_episodes": 7,
                "current_location": "河南省 漯河市 郾城区 海河路22号",
                "nav_dest_name": "由伟",
                "nav_dest_address": "广西壮族自治区 宁原市 饶平县 仁巷59号 23室",
                "battery": 86,
                "tts_language": "voluptate in dolore ipsum"
            }
        }
        body = json.dumps(intent_request_body, ensure_ascii=False)
        sign = self.generate_signature(self.sk, method, querystr, body)
        et_log.info(f"签名生成成功{sign}")
        headers = {
            "X-Signature": f'{self.ak}:{sign}',
            "Device-Id": f"{self.device_id}",
            "Vehicle-Type": f"{self.vehicle_type}",
            "Content-Type": "application/json"
        }
        try:
            start_time = time.time()
            response = requests.post(
                f'{self.intent_url}?{querystr}',
                headers=headers,
                data=body.encode('utf-8'),
                timeout=20,
                stream=False  # 启用流式响应以处理SSE格式数据
            )
            # print(f"rescode{response.status_code}")
            # print(f"{response.request.url}")
            # print(f"{response.request.headers}")
            if response.status_code == 200:
                response_data = response.json().get("data")
                intent_type = response_data.get("intent").get("intent_type")
                question_id = response_data.get("question_id")
                log_id = response_data.get("debug_info").get("log_id")
                result_list, end_time, first_info_time = self.plugin_poi_search(chat_id, question_id, intent_type,
                                                                                log_id)
                first_res_time = round(float(first_info_time - start_time), 2)
                res_time = round(float(end_time - start_time), 2)

                merged_text = ''
                debug_info = ''
                tts_text = ''
                summary = ''
                for item in result_list:
                    if not summary and item.get('stage', '') == 'summary':
                        formatted_summary = self.format_summary_response(item)
                        summary = formatted_summary
                    elif not debug_info and item.get('debug_info', ''):
                        debug_info = item.get('debug_info', '')
                    else:
                        tts_text += item.get('tts_text', '')

                merged_text += f'以下是tts_text内容：\n{tts_text}\n'
                merged_text += f'以下是summary内容：\n{summary}\n'
                merged_text += f'以下是debug info：\n{debug_info}'
                # print(merged_text)
                final_intent_type = intent_type
                item_result = self.build_result_item(str(self.item._id), self.task_id, self.result_id, self.item.expected_category,
                                                     final_intent_type,
                                                     (self.item).expected_answer, merged_text,
                                                     first_res_time, res_time, question_id,
                                                     self.do_user, self.oem_codes, self.conf_id)
                item_result["result_answer"] = 0  # 回答结果
                if merged_text:
                    item_result["result_answer"] = 1
                item_result["result_final"] = item_result["result_answer"]  # 最终结果
                et_log.info(f"Volkswagen_context_item_result:{item_result}")
                # 记录每条数据的执行结果
                item_result_id = TestItemResultService.insert_one(item_result)
                et_log.info(f"ByteDanceAdapter:reject insert_one item_result_id:{item_result_id}")
                return item_result_id

            else:
                raise Exception(f"API调用失败，HTTP状态码: {response.status_code}")
        except Exception as e:
            et_log.error(f"API调用失败: {e}")
            return None

    def format_summary_response(self, summary_data):
        """
        格式化summary响应数据
        """
        try:
            content = summary_data.get("content", "")
            observation_str = summary_data.get("observation", "{}")

            # 解析observation字符串为JSON对象
            observation = json.loads(observation_str)

            # 从observation中提取POI列表
            poi_list = observation.get("list", [])

            # 创建一个以poi_id为键的字典，便于快速查找
            poi_dict = {poi["poi_id"]: poi for poi in poi_list}

            # 解析content中的信息
            content_items = re.findall(r'(\d+)\. 地名：(.+?) ID：(\d+)', content)

            # 格式化输出
            result = []
            result.append("## 景点信息列表\n")

            for index, (num, name, poi_id) in enumerate(content_items, 1):
                result.append(f"{index}. **{name}**")
                result.append(f"   - ID: {poi_id}")

                # 从observation数据中获取详细信息
                poi_info = poi_dict.get(poi_id)
                if poi_info:
                    # 提取详细信息
                    province = poi_info.get('province', '')
                    city = poi_info.get('city', '')
                    district = poi_info.get('district', '')
                    address = poi_info.get('address', '')
                    amap_poi_id = poi_info.get('amap_poi_id', '')
                    type_name = poi_info.get('type_name', '')
                    has_video = poi_info.get('has_video', False)

                    # 格式化地址信息
                    full_address = "".join([province, city, district, address])
                    if full_address:
                        result.append(f"   - 地址: {full_address}")

                    # 添加其他信息
                    if amap_poi_id and amap_poi_id != poi_id:
                        result.append(f"   - 高德POI ID: {amap_poi_id}")

                    if type_name:
                        result.append(f"   - 类型: {type_name}")

                    result.append(f"   - 视频资源: {'有' if has_video else '无'}")

                result.append("")

            return '\n'.join(result)
        except Exception as e:
            et_log.error(f"格式化summary响应失败: {e}")
            return content  # 出错时返回原始content

    def generate_signature(self, sk: str, method: str, querystr: str, body: Optional[str]) -> str:
        """生成请求签名"""
        try:
            # 对查询参数按字典序排序
            a = querystr.split("&")
            a.sort()
            sortedquerystr = "&".join(a)

            # 构建待签名字符串
            strtosign = method + "\n" + sortedquerystr + "\n"
            if body is not None and len(body) > 0:
                m = hashlib.md5()
                m.update(body.encode("utf8"))
                strtosign += m.hexdigest() + "\n"

            # 计算HMAC-SHA256签名
            h = hmac.new(sk.encode("utf8"), strtosign.encode("utf8"), sha256).digest()
            signature = base64.b64encode(h).decode()
            et_log.info("Generated signature successfully")
            return signature
        except Exception as e:
            error_msg = f"Signature generation failed: {e}"
            et_log.error(error_msg, exc_info=True)

    def plugin_poi_search(self, chat_id, question_id, intent_type, intent_log_id):
        result_list = []
        flag = False
        method = "POST"
        first_info_time = None
        timestamp = int(time.time())
        random_integer = random.randint(0, 65535)
        querystr = f"_timestamp={timestamp}&_nonce={random_integer}"
        plugin_request_body = {
            "query": self.prompt,
            "chat_id": chat_id,
            "question_id": question_id,
            "intent_type": intent_type,
            "intent_log_id": intent_log_id,
            "history": [
            ],
            "car_info": {
                "vehicle_name": "掀背车",
                "sound_zone": "enim laboris",
                "audio_name": "居奕辰",
                "audio_producer": "laborum et",
                "current_location": "上海闵行平金中心一号楼",
                "nav_dest_name": "谌国良",
                "nav_dest_address": "北京市海淀区北三环西路甲18号",
                "home_lng_lat": {
                    "longitude": 121.474000,
                    "latitude": 31.230001
                },
                "battery": 55,
                "tts_language": "id et"
            },
            "plugin_params": "{\"poi_summary_data_limit\":5}"
        }
        body = json.dumps(plugin_request_body, ensure_ascii=False)
        sign = self.generate_signature(self.sk, method, querystr, body)
        et_log.info(f"签名生成成功{sign}")
        headers = {
            "X-Signature": f'{self.ak}:{sign}',
            "Device-Id": f"{self.device_id}",
            "Vehicle-Type": f"{self.vehicle_type}",
            "Content-Type": "application/json"
        }
        try:
            et_log.info(f"intent-url{self.plugin_stream_url}")
            response = requests.post(
                f'{self.plugin_stream_url}?{querystr}',
                headers=headers,
                data=body.encode('utf-8'),
                timeout=20,
                stream=True
            )
            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                        decoded_line = line.decode('utf-8')
                        if decoded_line.startswith('data:'):
                            if not flag:
                                first_info_time = time.time()
                                flag = True
                            event_data = json.loads(decoded_line[5:])
                            result_list.append(event_data)
                end_time = time.time()
            else:
                raise Exception(f"API调用失败，HTTP状态码: {response.status_code}")
            return result_list, end_time, first_info_time
        except Exception as e:
            et_log.error(f"API调用失败: {e}")
            raise e

    def build_result_item(self, item_id, task_id, task_result_id, expected_category, actual_category, expected_answer,
                          actual_answer,
                          first_res_time, qa_use_time, log_id, question_id, do_user=SYS_USER, oem_codes="", conf_id=""):
        et_log.info(
            f"VolkswagenRoutePerception:build_result_item item_id:{item_id} task_id:{task_id} task_result_id:{task_result_id} "
            f"conf_id:{conf_id} expected_category:{expected_category} actual_category:{actual_category} "
            f"expected_answer:{expected_answer} actual_answer:{actual_answer} "
            f"first_res_time:{first_res_time} qa_use_time:{qa_use_time} log_id:{log_id} question_id:{question_id}")
        if not CTX_OEM_CODES.get() and len(oem_codes) > 0:
            CTX_OEM_CODES.set(oem_codes)
        if not CTX_USER_ID.get():
            CTX_OEM_CODES.set(do_user)
        return {"parent_id": "", "data_set_item_id": str(item_id), "task_id": task_id,
                "task_result_id": str(task_result_id), "actual_task": "", "actual_category": actual_category,
                "actual_answer": actual_answer, "result_answer": 0, "answer_score": 0,
                "qa_recall": 0, "qa_use_time": qa_use_time, "recall_id": log_id,
                "remark": f"log_id:{log_id}|question_id:{question_id}", "first_res_time": first_res_time,
                "do_user": do_user, "re_interval_time": 0, "is_websearch": 0}


if __name__ == "__main__":
    class ConfigInfo:
        model_ak = "Byteacdfd2ac80148034f53f7a46133330ca"
        model_sk = "oNtHJuSUpNAgVQyiGvqPaCfcpHmgWezppUvFOxdiQOCs"
        model_url = "http://120.27.137.224:8989/dpfm/v1/plugin/do/prepare"
        model_params = """{"Device_Id": "1000428",
                        "Vehicle_Type": "test_vehicle",
                        "plugin_stream_url": "http://120.27.137.224:8989/dpfm/v1/plugin/do/stream"}"""

    class DataItem:
        question = "好想唱歌，帮我找一下中央大街有哪些KTV"


    tool = VolkswagenPoiSearch(ConfigInfo, DataItem, "", "", "", "", "")
    tool.intent_reject()
