from uuid import uuid4
import time
import random
import json
import requests
from com.exturing.ai.test.comm.comm_constant import CTX_USER_ID, CTX_OEM_CODES, SYS_USER
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.test_config import TestConfigInfoModel
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService
import hashlib
import hmac
from typing import Optional
from hashlib import sha256
import base64
import re


class Volkswagen_context:
    """大众意图识别"""

    def __init__(self, config_info: TestConfigInfoModel, item:EtDataSetItem, task_id, result_id, conf_id, do_user, oem_codes=""):
        model_params_str = config_info.model_params
        model_params_dict = json.loads(model_params_str)
        device_id = model_params_dict.get("Device_Id")
        vehicle_type = model_params_dict.get("Vehicle_Type")
        arb_url = model_params_dict.get("arb_url")
        self.device_id = device_id
        self.vehicle_type = vehicle_type
        self.arb_url = arb_url
        self.ak = config_info.model_ak
        self.sk = config_info.model_sk
        self.intent_url = config_info.model_url
        self.prompt = item.question
        self.task_id = task_id
        self.result_id = result_id
        self.conf_id = conf_id
        self.item = item
        self.do_user = do_user
        self.oem_codes = oem_codes



    def intent_reject(self):
        """
        大众适配=意图识别
        """
        et_log.info(f"Volkswagen_context:intention task_id:{self.task_id} result_id:{self.result_id} conf_id:{self.conf_id}")
        method = "POST"
        timestamp = int(time.time())
        random_integer = random.randint(0, 65535)
        chat_id = uuid4().hex
        question_id = uuid4().hex
        querystr = f"_timestamp={timestamp}&_nonce={random_integer}&chat_id={chat_id}&question_id={question_id}"
        params = {
            "query": self.prompt,
            "history": [
                {
                    "q": "eu exercitation consectetur enim quis",
                    "a": "enim mollit sit adipisicing"
                },
                {
                    "q": "ad consectetur irure qui",
                    "a": "aute exercitation ut"
                }
            ],
            "car_info": {
                "vehicle_name": "掀背车",
                "sound_zone": "consequat culpa veniam ullamco deserunt",
                "audio_name": "荤熙成",
                "audio_producer": "adipisicing Lorem occaecat enim dolor",
                "current_location": "nulla elit est dolor aliqua",
                "nav_dest_name": "由伟",
                "nav_dest_address": "广西壮族自治区 宁原市 饶平县 仁巷59号 23室",
                "battery": 86,
                "tts_language": "voluptate in dolore ipsum"
            }
        }
        body = json.dumps(params, ensure_ascii=False)
        sign = self.generate_signature(self.sk, method, querystr, body)
        et_log.info(f"签名生成成功{sign}")
        headers = {
            "X-Signature": f'{self.ak}:{sign}',
            "Device-Id": f"{self.device_id}",
            "Vehicle-Type": f"{self.vehicle_type}",
            "Content-Type": "application/json"
        }
        try:
            start_time = time.time()
            response = requests.post(
                f'{self.intent_url}?{querystr}',
                headers=headers,
                data=body.encode('utf-8'),
                timeout=20,
                stream=True  # 启用流式响应以处理SSE格式数据
            )
            print(f"rescode{response.status_code}")
            if response.status_code == 200:
                print(response.text)
                result_list,end_time,first_info_time = self.arbitration(chat_id,question_id)
                merged_text = ''.join(result_list)
                merged_text = re.sub(r'\n+', '\n', merged_text).strip()
                merged_text = re.sub(r'(\d+\.)', r'\n\1', merged_text).lstrip('\n')
                print(merged_text)
                first_res_time = round(float(first_info_time-start_time),2)
                res_time = round(float(end_time-start_time),2)
                item_result = self.build_result_item(str(self.item._id), self.task_id, self.result_id, "",
                                                    "",
                                                    (self.item).expected_answer, merged_text,
                                                    first_res_time, res_time, question_id,
                                                    self.do_user, self.oem_codes, self.conf_id)
                item_result["result_answer"] = 0  # 回答结果
                if merged_text:
                    item_result["result_answer"] = 1
                item_result["result_final"] = item_result["result_answer"]  # 最终结果
                et_log.info(f"Volkswagen_context_item_result:{item_result}")
                # 记录每条数据的执行结果
                item_result_id = TestItemResultService.insert_one(item_result)
                et_log.info(f"ByteDanceAdapter:reject insert_one item_result_id:{item_result_id}")
                return item_result_id

            else:
                raise Exception(f"API调用失败，HTTP状态码: {response.status_code}")
        except Exception as e:
            et_log.error(f"API调用失败: {e}")
            return None, None, None


    def arbitration(self, chat_id:str, question_id:str):
        flag = False
        first_info_time = ""
        end_time = ""
        result_list = []
        method = "POST"
        et_log.info(f"chat_id：{chat_id}以及question_id：{question_id}\n通过意图拒识校验成功")
        timestamp = int(time.time())
        random_integer = random.randint(0, 65535)
        querystr = f"_timestamp={timestamp}&_nonce={random_integer}&chat_id={chat_id}&question_id={question_id}"
        params = {
            "cid": "1",
            "sid": "1",
            "stmid": "1",
            "query": self.prompt,
            "first_round": False,
            "vad_enabled": False,
            "history": [
                {
                    "q": "eu exercitation consectetur enim quis",
                    "a": "enim mollit sit adipisicing"
                },
                {
                    "q": "ad consectetur irure qui",
                    "a": "aute exercitation ut"
                }
            ],
            "car_info": {
                "vehicle_name": "掀背车",
                "sound_zone": "consequat culpa veniam ullamco deserunt",
                "audio_name": "荤熙成",
                "audio_producer": "adipisicing Lorem occaecat enim dolor",
                "current_location": "nulla elit est dolor aliqua",
                "nav_dest_name": "由伟",
                "nav_dest_address": "广西壮族自治区 宁原市 饶平县 仁巷59号 23室",
                "battery": 86,
                "tts_language": "voluptate in dolore ipsum"
            }
        }
        body = json.dumps(params, ensure_ascii=False)
        sign = self.generate_signature(self.sk,method, querystr, body)
        headers = {
            "X-Signature": f'{self.ak}:{sign}',
            "Device-Id": f"{self.device_id}",
            "Vehicle-Type": f"{self.vehicle_type}",
        }
        try:
            response = requests.post(
                f'{self.arb_url}?{querystr}',
                headers=headers,
                data=body.encode('utf-8'),
                stream=True  # 启用流式响应以处理SSE格式数据
            )
            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                            decoded_line = line.decode('utf-8')
                            if decoded_line.startswith('data:'):
                                if not flag:
                                    first_info_time = time.time()
                                    flag = True
                                event_data = json.loads(decoded_line[5:])
                                if 'payload' in str(event_data):
                                    payload = event_data['payload']
                                    text = payload.get('stream_tpp', {}).get('text', '')
                                    text_bytes = base64.b64decode(text)
                                    text_str = text_bytes.decode('utf-8')
                                    inner_json = json.loads(text_str)
                                    value = self.find_tts(inner_json)
                                    if value:
                                        result_list.append(value)
                end_time =time.time()

            else:
                raise Exception(f"API调用失败，HTTP状态码: {response.status_code}")
            return result_list,end_time,first_info_time
        except Exception as e:
            et_log.error(f"API调用失败: {e}")










    def generate_signature(self,sk: str, method: str, querystr: str, body: Optional[str]) -> str:
        """生成请求签名"""
        try:
            # 对查询参数按字典序排序
            a = querystr.split("&")
            a.sort()
            sortedquerystr = "&".join(a)

            # 构建待签名字符串
            strtosign = method + "\n" + sortedquerystr + "\n"
            if body is not None and len(body) > 0:
                m = hashlib.md5()
                m.update(body.encode("utf8"))
                strtosign += m.hexdigest() + "\n"

            # 计算HMAC-SHA256签名
            h = hmac.new(sk.encode("utf8"), strtosign.encode("utf8"), sha256).digest()
            signature = base64.b64encode(h).decode()
            et_log.info("Generated signature successfully")
            return signature
        except Exception as e:
            error_msg = f"Signature generation failed: {e}"
            et_log.error(error_msg, exc_info=True)

    def find_tts(self,obj):
        if isinstance(obj, dict):
            if 'tts_text' in obj:
                return obj['tts_text']
            for v in obj.values():
                res = self.find_tts(v)
                if res is not None:
                    return res
        elif isinstance(obj, list):
            for item in obj:
                res = self.find_tts(item)
                if res is not None:
                    return res
        return None

    def build_result_item(self,item_id, task_id, task_result_id, expected_category, actual_category, expected_answer, actual_answer,
                          first_res_time, qa_use_time, log_id, question_id, do_user=SYS_USER, oem_codes="", conf_id=""):
        et_log.info(f"ByteDanceAdapter:build_result_item item_id:{item_id} task_id:{task_id} task_result_id:{task_result_id} "
                    f"conf_id:{conf_id} expected_category:{expected_category} actual_category:{actual_category} "
                    f"expected_answer:{expected_answer} actual_answer:{actual_answer} "
                    f"first_res_time:{first_res_time} qa_use_time:{qa_use_time} log_id:{log_id} question_id:{question_id}")
        if not CTX_OEM_CODES.get() and len(oem_codes) > 0:
            CTX_OEM_CODES.set(oem_codes)
        if not CTX_USER_ID.get():
            CTX_OEM_CODES.set(do_user)
        return {"parent_id":"", "data_set_item_id": str(item_id), "task_id": task_id,
                "task_result_id": str(task_result_id), "actual_task": "", "actual_category": actual_category,
                "actual_answer": actual_answer, "result_answer": 0, "answer_score": 0,
                "qa_recall": 0, "qa_use_time": qa_use_time, "recall_id": log_id,
                "remark": f"log_id:{log_id}|question_id:{question_id}", "first_res_time": first_res_time,
                "do_user": do_user, "re_interval_time": 0, "is_websearch": 0}









