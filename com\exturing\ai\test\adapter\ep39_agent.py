import base64
import hashlib
import hmac
import json
import random
import string
import time
from typing import Optional
from uuid import uuid4

import requests

from com.exturing.ai.test.comm.comm_constant import SYS_USER, CTX_OEM_CODES, CTX_USER_ID
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.test_config import TestConfigInfoModel
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService


class EP39Agent:
    def __init__(self, config_info: TestConfigInfoModel, item: EtDataSetItem, task_id, result_id, conf_id, do_user,
                 oem_codes=""):
        model_params_str = config_info.model_params
        model_params_dict = json.loads(model_params_str)
        self.device_id = model_params_dict.get("Device_Id")
        self.arbitration_url = model_params_dict.get("arbitration_url")
        self.vehicle_type = model_params_dict.get("Vehicle_Type")
        self.vehicle_name = model_params_dict.get("Vehicle_name")
        self.app_id = model_params_dict.get("app_id")
        self.channel = model_params_dict.get("channel")
        self.vehicle_id = model_params_dict.get("vehicle_id")

        self.ak = config_info.model_ak
        self.sk = config_info.model_sk
        self.intent_url = config_info.model_url
        self.prompt = item.question
        self.task_id = task_id
        self.result_id = result_id
        self.conf_id = conf_id
        self.item = item
        self.do_user = do_user
        self.oem_codes = oem_codes

    def generate_signature(self, sk: str, method: str, querystr: str, body: Optional[str]) -> str:
        """生成请求签名"""
        try:
            # 对查询参数按字典序排序
            a = querystr.split("&")
            a.sort()
            sortedquerystr = "&".join(a)

            # 构建待签名字符串
            strtosign = method + "\n" + sortedquerystr + "\n"
            if body is not None and len(body) > 0:
                m = hashlib.md5()
                m.update(body.encode("utf8"))
                strtosign += m.hexdigest() + "\n"

            # 计算HMAC-SHA256签名
            h = hmac.new(sk.encode("utf8"), strtosign.encode("utf8"), hashlib.sha256).digest()
            signature = base64.b64encode(h).decode()
            et_log.info("Generated signature successfully")
            return signature
        except Exception as e:
            error_msg = f"Signature generation failed: {e}"
            et_log.error(error_msg, exc_info=True)

    def intent_reject(self):
        et_log.info(
            f"EP39:intention task_id:{self.task_id} result_id:{self.result_id} conf_id:{self.conf_id}")
        method = "POST"
        timestamp = int(time.time())
        random_integer = random.randint(0, 65535)
        self.chat_id = f"chattest"
        self.question_id = uuid4().hex
        querystr = f"vehicle_id={self.vehicle_id}&channel={self.channel}&app_id={self.app_id}&vehicle_type={self.vehicle_type}&vehicle_name={self.vehicle_name}&_timestamp={timestamp}&_nonce={random_integer}&chat_id={self.chat_id}&question_id={self.question_id}"
        intent_request_body = {
            "query": self.prompt,
            "history": [
                {
                    "q": "你好",
                    "a": "嗯"
                }
            ],
            "car_info": {
                "nav_dest_address": "北京市海淀区北三环西路甲18号",
                "video_name": "奔跑吧兄弟",
                "audio_name": "江南"
            },
            "extra": {
                "thinking_type": "disabled"
            }
        }
        body = json.dumps(intent_request_body, ensure_ascii=False)
        sign = self.generate_signature(self.sk, method, querystr, body)
        et_log.info(f"签名生成成功{sign}")
        headers = {
            "X-Signature": f'{self.ak}:{sign}',
            "Device-Id": f"{self.device_id}",
            "Vehicle-Type": f"{self.vehicle_type}",
            "Content-Type": "application/json"
        }
        try:
            start_time = time.time()
            response = requests.post(
                f'{self.intent_url}?{querystr}',
                headers=headers,
                data=body.encode('utf-8'),
                timeout=20,
                stream=False
            )
            if response.status_code != 200:
                et_log.error(f"API调用失败，状态码: {response.status_code}")
                return None, None, None
            intent_time = round(float(time.time() - start_time), 2)
            return start_time, intent_time, response.json()
        except Exception as e:
            et_log.error(f"API调用失败: {e}")
            return None, None, None

    def arbitration(self):
        et_log.info(
            f"EP39:arbitration task_id:{self.task_id} result_id:{self.result_id} conf_id:{self.conf_id}")
        method = "POST"
        timestamp = int(time.time())
        random_integer = random.randint(0, 65535)
        # question_id = uuid4().hex
        querystr = f"vehicle_id={self.vehicle_id}&channel={self.channel}&app_id={self.app_id}&vehicle_type={self.vehicle_type}&vehicle_name={self.vehicle_name}&_timestamp={timestamp}&_nonce={random_integer}&chat_id={self.chat_id}&question_id={self.question_id}"
        request_body = {
            "query": self.prompt,
            "answer": "",
            "observation": """{\"functions\":[{\"function_code\":\"驾驶模式设置\",\"params\":[{\"action\":\"打开\",\"mode\":\"运动\"}],\"data\":\"{\\\"service\\\":\\\"app\\\",\\\"semantic\\\":{\\\"slots\\\":{\\\"mode\\\":\\\"驾驶模式\\\",\\\"name\\\":\\\"总里程\\\"}},\\\"operation\\\":\\\"SET\\\"}\"}]}"""
        }
        body = json.dumps(request_body, ensure_ascii=False)
        sign = self.generate_signature(self.sk, method, querystr, body)
        et_log.info(f"签名生成成功{sign}")
        headers = {
            "X-Signature": f'{self.ak}:{sign}',
            "Device-Id": f"{self.device_id}",
            "Vehicle-Type": f"{self.vehicle_type}",
            "Content-Type": "application/json"
        }
        try:
            response = requests.post(
                f'{self.arbitration_url}?{querystr}',
                headers=headers,
                data=body.encode('utf-8'),
                timeout=20,
                stream=True  # 启用流式响应以处理SSE格式数据
            )
            flag = False
            result_list = []
            first_info_time = ''
            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                        print(line)
                        if not flag:
                            first_info_time = time.time()
                            flag = True
                        result_list.append(line)
                end_time = time.time()
                return first_info_time, end_time, result_list
            else:
                return None, None, None
        except Exception as e:
            et_log.error(f"API调用失败: {e}")
            return None, None, None

    def build_result_item(self, item_id, task_id, task_result_id, expected_category, actual_category, expected_answer,
                          actual_answer,
                          first_res_time, qa_use_time, intent_time, log_id, question_id, do_user=SYS_USER, oem_codes="", conf_id=""):
        et_log.info(
            f"VolkswagenRoutePerception:build_result_item item_id:{item_id} task_id:{task_id} task_result_id:{task_result_id} "
            f"conf_id:{conf_id} expected_category:{expected_category} actual_category:{actual_category} "
            f"expected_answer:{expected_answer} actual_answer:{actual_answer} "
            f"first_res_time:{first_res_time} qa_use_time:{qa_use_time} log_id:{log_id} question_id:{question_id}")
        if not CTX_OEM_CODES.get() and len(oem_codes) > 0:
            CTX_OEM_CODES.set(oem_codes)
        if not CTX_USER_ID.get():
            CTX_OEM_CODES.set(do_user)
        return {"parent_id": "", "data_set_item_id": str(item_id), "task_id": task_id,
                "task_result_id": str(task_result_id), "actual_task": "", "actual_category": actual_category,
                "actual_answer": actual_answer, "result_answer": 0, "answer_score": 0,
                "qa_recall": 0, "qa_use_time": qa_use_time, "recall_id": log_id,
                "remark": f"log_id:{log_id}|question_id:{question_id}|intent_time:{intent_time}", "first_res_time": first_res_time,
                "do_user": do_user, "re_interval_time": 0, "is_websearch": 0}

    def format_observation(self, data):
        if data.get('functions'):
            for function in data['functions']:
                if function.get('data'):
                    function['data'] = json.loads(function['data'])
            return data

    def chat(self):
        start_time, intent_time, intent_response = self.intent_reject()
        if not start_time or not intent_time or not intent_response and not intent_response.get('msg', '') == 'success':
            et_log.error(f"EP39:intent_reject api error task_id:{self.task_id} result_id:{self.result_id} conf_id:{self.conf_id}")
            return None
        time.sleep(0.5)     # 跟开发郭铭霖沟通，意图和仲裁之间必须加等待时间，暂设500ms
        first_info_time, end_time, result_list = self.arbitration()
        if not first_info_time or not end_time or not result_list:
            et_log.error(f"EP39:arbitration api error task_id:{self.task_id} result_id:{self.result_id} conf_id:{self.conf_id}")
            return None
        first_res_time = round(float(first_info_time - start_time), 2)
        res_time = round(float(end_time - start_time), 2)
        result_text = ''    # 最后生成结果
        tts_text = ''   # tts_text回复内容
        is_reject = False   # 是否拒识
        is_reject_get_flag = False   # 是否拒识
        is_llm_intent = False   # 是否深度思考
        log_id = ''
        intent_type = ""
        origin_intent_type = ""
        observation_list = []
        hints_list = []
        for item in result_list:
            decoded_str = item.decode('utf-8')
            json_str = decoded_str[6:]
            data_dict = json.loads(json_str)['data']
            # print(data_dict)
            if not log_id:
                log_id = data_dict.get('log_id', '')
            if not is_reject_get_flag:
                is_reject = data_dict.get('rejection', '')
                is_reject_get_flag = True
            if not intent_type:
                intent_type = data_dict.get('intent_type', '')
            if not origin_intent_type:
                origin_intent_type = data_dict.get('origin_intent_type', '')
            if not is_llm_intent:
                is_llm_intent = True if data_dict.get('extra').get('is_llm_intent', '') == 'true' else False
            if data_dict.get('observation', ''):
                observation_list.append(self.format_observation(data_dict.get('observation')))
            if data_dict.get('hints', []):
                hints_list.extend(data_dict.get('hints'))
            text = data_dict.get('content', '')
            if text:
                tts_text += text
        result_text += f'ttx内容：{tts_text}\n\n是否拒识：{is_reject}，是否深度思考：{is_llm_intent}，识别意图：{intent_type}，原始意图：{origin_intent_type}\n\nobservation内容：\n{observation_list}\n\nhints内容：\n{hints_list}'
        item_result = self.build_result_item(str(self.item._id), self.task_id, self.result_id,
                                             self.item.expected_category,
                                             intent_type,
                                             (self.item).expected_answer, result_text,
                                             first_res_time, res_time, intent_time, log_id, self.question_id,
                                             self.do_user, self.oem_codes, self.conf_id)
        item_result["result_answer"] = 0  # 回答结果
        if result_text:
            item_result["result_answer"] = 1
        item_result["result_final"] = item_result["result_answer"]  # 最终结果
        et_log.info(f"Volkswagen_context_item_result:{item_result}")
        # 记录每条数据的执行结果
        item_result_id = TestItemResultService.insert_one(item_result)
        et_log.info(f"ByteDanceAdapter:reject insert_one item_result_id:{item_result_id}")
        return item_result_id

if __name__ == '__main__':
    class ConfigInfo:
        model_ak = "roewevh03sx4mtjlaabn8qgnw58chhkb"
        model_sk = "xl2xesru13kddcuaz93dx1f16o6ooh7u"
        model_url = "http://121.43.187.212:8987/dpfm/v1/plugin/do/prepare"
        model_params = """{
            "Device_Id": "1000427",
            "Vehicle_Type": "ep39",
            "Vehicle_name": "EP39",
            "arbitration_url": "http://121.43.187.212:8991/dpfm/v1/arbitration",
            "app_id": "99480",
            "channel": "roewe",
            "vehicle_id": "vehicle_id_18116"
        }"""


    class DataItem:
        question = "我是什么星座的"


    tool = EP39Agent(ConfigInfo, DataItem, "", "", "", "", "")
    tool.chat()
