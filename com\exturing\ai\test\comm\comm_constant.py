import contextvars
import os
from enum import Enum
from bson import ObjectId
from typing import List

# 环境变量
LOG_PATH = os.environ["LOG_PATH"]
MONGODB_URL = os.environ["MONGODB_URL"]
MONGODB_DB = os.environ["MONGODB_DB"]
SENDER_MAIL = os.environ.get("SENDER_MAIL")
SENDER_PW = os.environ.get("SENDER_PW")
MAIL_SMTP = os.environ.get("SMTP")

# 默认URL前缀
URL_PREFIX = "tapi"

# 默认主键ObjectId值，以常量代替
DEF_OID = ObjectId("df77f839588e9bd88e8be59d")
# 默认系统用户
SYS_USER = -1

# 字典集合，维度字典代码
DIC_CODE_DIMENSION = "dm-dimension" # 维度
DIC_CODE_MODEL = "dm-model" # 模块
DIC_CODE_BRANCH = "dm-branch" # 车型分支（测试任务 环境对应使用）

# 1：模型，2：应用，3：云平台，4：端侧
ENV_TYPE_LLM = 1 # 模型
ENV_TYPE_AGENT = 2 # 应用
ENV_TYPE_PLATFORM = 3 # 平台
ENV_TYPE_APP = 4 # 端侧

# 适配代码，根据该代码选择走不同的适配处理逻辑
ENV_ADAPTER_CODE_CATEGORY = "dsk-llm-category" # 适配 中枢分类
ENV_ADAPTER_CODE_AUTOTEST = "agent-llm-test" # 适配 autotest
ENV_ADAPTER_CODE_HUB = "agent-llm-hub" # 适配 中枢agent
ENV_ADAPTER_CODE_CONTEXT = "dsk-llm-context" # 适配 语义上下文
ENV_mind2 = "mind2"
ENV_mind2agent = "mind2agent"

ENV_ADAPTER_CODE_QIANFAN3_5 = "model-qianfan3.5"
ENV_ADAPTER_CODE_QIANFAN4_0 = "model-qianfan4.0"
ENV_ADAPTER_CODE_QIANFAN4_5 = "model-qianfan4.5"
ENV_ADAPTER_CODE_QIANWEN2_5 = "model-qianwen2.5"
ENV_ADAPTER_CODE_QIANWEN3_32B = "Qwen3-32B"
ENV_ADAPTER_CODE_QIANWENA3B = "Qwen3-30B-A3B"
ENV_ADAPTER_CODE_QIANWENQwQ_32B = "QwQ-32B"
ENV_ADAPTER_CODE_DOUBAO = "model-doubao"
ENV_ADAPTER_CODE_DSR1 = "model-deepseekR1"
ENV_ADAPTER_CODE_DSV3 = "model-deepseekV3"
ENV_ADAPTER_CODE_BD_INTENT = "intent"# 字节 意图识别
ENV_ADAPTER_CODE_BD_REJECT = "reject_rec"# 字节 拒识
ENV_ADAPTER_CODE_BD4HW_INTENT = "HUAWEI_INTENT"# 字节-华为 意图识别
ENV_ADAPTER_CODE_BD4HW_REJECT = "HUAWEI_REJECT"# 字节-华为 拒识
ENV_ADAPTER_CODE_HUAWEI_CONTEXT = "HUAWEI-LLM-CONTEXT"# 华为指代
ENV_ADAPTER_CODE_VOLKSWAGEN_CONTEXT = "volkswagen_context"  #大众闲聊
ENV_ADAPTER_CODE_VOLKSWAGEN_IMAGE = "volkswagen_image"   #大众生图
ENV_ADAPTER_CODE_BENGTENG_TRIP = "benteng_trip_plan"   #奔腾行程规划
ENV_ADAPTER_CODE_HYUNDAI_INTENT_REJECT = "hyundai_intent_reject"
ENV_ADAPTER_CODE_HYUNDAI_NEWS = "hyundai_news"
ENV_ADAPTER_CODE_HYUNDAI_TEXT_TO_SCENE = "hyundai_text_to_scene"
ENV_ADAPTER_CODE_HYUNDAI_FC = "hyundai_fc"
ENV_ADAPTER_CODE_HYUNDAI_MEDIA = "hyundai_media"
ENV_ADAPTER_CODE_HYUNDAI_AI_CANVAS = "hyundai_ai_canvas"
ENV_ADAPTER_CODE_VOLKSWAGEN_POI_SEARCH = "volkswagen_poi_search"   #大众POI搜索
ENV_ADAPTER_CODE_EP39_AGENT = "ep39_agent"   #ep39agent
# 字节-华为 私有接口DeviceId
HUOSHAN_HUAWEI_DEVICE_ID = "1000506"

# 百度千帆
QIANFANG_API_KEY = "LjdjpvXXrwsWmdOSPj1mxbeX" # API KEY
QIANFANG_SECRET_KEY = "BgUbIQ47iUPmveroNxhTKsuydYbUcIFj" # SECRET_KEY

#百度千帆
QIANFAN_MODELCONTROL_URL = "https://qianfan.baidubce.com/v2"
QIANFAN_MODELCONTROL_API_KEY = "bce-v3/ALTAK-C4aC0sePgOrgnQgqVdubR/84edf53990c45d2440cb969c35de3eaf08387952"
# ACCESS_TOKEN_UR
QIANFANG_ACCESS_TOKEN_URL = f"https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={QIANFANG_API_KEY}&client_secret={QIANFANG_SECRET_KEY}"
# 调用流式API接口计算语义相似度URL
QIANFANG_SIMILARITY_URL = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-4.0-turbo-128k?access_token={ACCESS_TOKEN}"
QIANFAN_35_URL = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-3.5-128k?access_token={ACCESS_TOKEN}"
# 豆包
DOUBAO_API_KEY = '38152498-88ec-42ee-a09f-cf0df75ad4b0'
DOUBAO_BOT_MODEL="bot-20240617091417-s5tzs"
DOUBAO_BASE_URL = "https://ark.cn-beijing.volces.com/api/v3"

# 火山
HUOSHAN_API_KEY = "extour2zmajhjksbgfa2s6mo6oolj3bf"
HUOSHAN_SECRET_KEY = "nv6onlu6ufyi6q9xpim8qu630bopi7b1"
HUOSHAN_CHANNNEl = "extour"
HUOSHAN_APPID = 99430
HUOSHAN_VEHICLE_ID = "121212"
HUOSHAN_intent_URL = "https://api-vehicle.volcengine.com/dpfm/v1/chat/intent"
HUOSHAN_PLAGIN_URL = "https://api-vehicle.volcengine.com/dpfm/v1/plugin/do/stream"


# mind2.0
MIND2_API_KEY = "none"
MIND2_BOT_MODEL="yt-model-32b-lora"
MIND2_BASE_URL = "https://u543582-8b14-6b01644a.bjc1.seetacloud.com:8443/deepseek_32B/v1"

# mind2.0agent
MIND2AGENT_URL = "http://************:9999/v1"
MIND2AGENT_BOT_MODEL = "deepseek-r1-distill-qwen-32b"


# 请求上下文中的oem_codes
CTX_OEM_CODES:contextvars.ContextVar[str] = contextvars.ContextVar("oem_codes", default="")
# 请求上下文中的oem_codes
CTX_USER_ID:contextvars.ContextVar[int] = contextvars.ContextVar("user_id", default=SYS_USER)
# 请求上下文中的文生图次数
CTX_USER_T2I_COUNT:contextvars.ContextVar[{}] = contextvars.ContextVar("user_t2i_count", default={})
# 全量oem_codes
ALL_OEM_CODES = "all,aispeech"

# 任务的数据集来源类型 select=选择的已有数据集
TASK_DATASET_TYPE_SEL = "select"
# 任务的数据集来源类型 build=根据条件自动随机构建
TASK_DATASET_TYPE_BUILD = "build"

# 任务自动执行周期
TASK_CYCLE_DAY = "day"
TASK_CYCLE_WEEK = "week"
TASK_CYCLE_MONTH = "month"
TASK_CYCLE_CUSTOM = "custom"

# 任务类型
EVAL_TYPE_DEF = 0  # 默认评估类型 单通道
EVAL_TYPE_MULTI = 1  # 多通道评估类型

# 任务触发器类型
SCHEDULER_TRIGGER_INTERVAL = "interval"
SCHEDULER_TRIGGER_CRON = "cron"
SCHEDULER_TRIGGER_DATE = "date"

# 系统配置字典code
DIC_GROUP_SYS_CONF = "sys_conf"
# 每日最大执行语料条目限制
DIC_CODE_MAX_RUN_ITEM = "max_run_item"
# 每日最大执行文生图t2i条目限制
DIC_CODE_MAX_T2I = "max_t2i"


# 阿里云OSS相关信息
OSS_REGION = os.environ.get("OSS_REGION")
OSS_BUCKET = os.environ.get("OSS_BUCKET")
OSS_ENDPOINT = os.environ.get("OSS_ENDPOINT")
OSS_AK = os.environ.get("OSS_ACCESS_KEY_ID")
OSS_SK = os.environ.get("OSS_ACCESS_KEY_SECRET")

# 阿里云OSS上传文件保存路径
UPLOAD_FILE_PATH = "test_ai_eval/upload_files/"

# auto_test 构建方式
AUTO_TEST_BUILD = "自动|自动生成" # 模型自动生成语料后构建数据集，（默认模式）
AUTO_TEST_SEL = "选择|挑选|随机" # 从原始语料按条件选择语料，然后构建数据集
# 自动评测数据标识
AUTO_TEST_TAG = "A-"

# test_task_result任务结果表任务结果状态
TEST_TASK_INIT = 0 # 初始
TEST_TASK_SUCCESS = 1 # 成功
TEST_TASK_FAIL = -1 # 失败

#商汤文生图
ST_URL = "ws://112.124.1.251:8766/llm"
#百度文生图
ENINE_IRAG_URL = "https://qianfan.baidubce.com/v2/images/generations"

# 提示词模板状态
PROMPT_TMP_STATUS_INIT = 0 # 待使用
PROMPT_TMP_STATUS_ENABLE = 1 # 启用
PROMPT_TMP_STATUS_DISABLE = -1 # 禁用

# 默认，通用评估提示词模板code
PROMPT_TMP_EVAL_CODE = "comm_eval_pt"




