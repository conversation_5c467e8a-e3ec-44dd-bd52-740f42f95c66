# service.py
from com.exturing.ai.test.model.sys_dict_item import SysDictItemModel, SysDictItemQueryModel
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.comm.log_tool import et_log

_doc = "sys_dict_item"


# 新增系统字典项
def create(data: SysDictItemModel) -> str:
    data_dict = data.model_dump()
    et_log.info(f"create sys_dict_item: {data_dict}")
    return MongoDBUtil.insert_one(_doc, data_dict)


# 分页查询系统字典项
def query_page(page_num: int = 1, page_size: int = 10, query: SysDictItemQueryModel = None):
    condition = query.get_query_condition()
    total = MongoDBUtil.find_count(_doc, condition)

    page = PageResult(page_num, page_size, total)

    if total > 0:
        result = MongoDBUtil.find_condition_page(
            _doc, condition, None, page.skip, page_size
        )
        result_list = list(result or [])
        json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]
        page.page_data = json_list

    return page


# 查询系统字典项
def query_list(query: SysDictItemQueryModel = None):
    condition = query.get_query_condition()

    result = MongoDBUtil.find_condition(_doc, condition)
    result_list = list(result or [])
    json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]

    return json_list


# 修改系统字典项
def update(id: str, data: SysDictItemModel) -> bool:
    data_dict = data.model_dump()

    data_dict["_id"] = id
    et_log.info(f"update sys_dict_item: {data_dict}")
    update_num = MongoDBUtil.update_one_pro(_doc, data_dict)

    if update_num.modified_count and update_num.modified_count > 0:
        return True
    else:
        return False


# 删除系统字典项
def delete(id: str) -> bool:
    et_log.info(f"delete sys_dict_item by id:{id}")
    return MongoDBUtil.delete_by_id(_doc, id, 0) > 0
