from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class SysDictModel(BaseDataModel):
    name: Optional[str] = None  # 字典名称
    code: Optional[str] = None  # 字典编码
    item_code_type: Optional[int] = None  # 字典项编码类型 1 字符串 2 数字
    desc: Optional[str] = None  # 字典描述


class SysDictQueryModel(BaseModel):
    name: Optional[str] = None

    def get_query_condition(self):
        condition = {}

        if self.name:
            condition["name"] = {"$regex": str(self.name).strip(), "$options": "i"}

        return condition
