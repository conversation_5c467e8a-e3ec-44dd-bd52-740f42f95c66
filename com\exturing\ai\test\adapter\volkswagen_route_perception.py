import base64
import hashlib
import hmac
import json
import random
import time
from hashlib import sha256
from typing import Optional
from uuid import uuid4
import math
import requests

from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES, CTX_USER_ID, SYS_USER
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.test_config import TestConfigInfoModel
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService

request_body = {
    "query": "",
    "llm_params": {
        "intent-custom-intent": {
            "system_prompt": [
                "角色\n你是汽车智能助手，你擅长将用户的指令转换为汽车功能的操作。\n\n任务\n我会给定一个函数列表，每个函数代表一种汽车控制功能，你需要结合对话上下文语境，理解用户当前的指令，在支持的汽车功能函数列表中选出一个函数，并输出函数名。\n\n输入格式\n输入为一段 user 与 assistant 的对话，其中 assistant 的回复会被选择性省略。\n\n输出要求\n你要输出汽车功能函数的名称\n当没有对应的功能函数可用时，则函数名为 '其他'\n\n支持的汽车功能函数列表\n1. 行程规划\n该函数用于帮助用户推荐指定地区的旅游路线和玩法规划。\n使用条件：\n用户必须明确请求助手进行行程规划，不能只表达自己的想法、诉求或行动，也不能只要求推荐地点\n用户明确要求规划的必须是长途旅游路线或者旅游计划\n用户的输入明确包含了关键词'行程'或'行程规划'或'行程'与'规划'两个关键词同时存在\n正例输入示例：\nuser：我想去北京旅行，帮我做一个三天的行程规划\n输出：trip_planning\n负例输入示例列表：\nuser：我下个月要去云南祭拜祖先\nuser：有哪里适合露营吗，推荐一个\n\n2. 沿途感知 \n该函数用于帮助用户查询附近指定方位的 POI 信息。\n使用条件：\n用户只有提到以下类别才可以查询：车辆、动物、植物、河流、湖泊、山、风景名胜、交通地名（路、桥、隧道、立交桥、环岛）、楼宇、住宅、商场、医院、学校\n用户需要明确表达地点的相对位置信息，包括的范围有：当前位置、上下前后左右、东南西北、周围、周边\n除了以上的情况，用户还可以输入人称代词（我、它）+方位词（上下前后左右、东南西北、周围、周边）来询问。\n正例输入示例列表：\nuser：右边的小区多少钱一平？\nuser：东边的河是什么河？\nuser：左前方是什么大楼？\nuser：我的左边是什么建筑？\nuser：它的右边是什么路？\nuser：前面是什么车？\nuser：前面黄色的建筑是什么？\nuser：前面是什么动物？\nuser：左边的植物是什么？\n输出：沿途感知\n负例输入示例列表：\nuser：帮我推荐一个附近的景点\nuser：前面这个咖啡店还不错"
            ],
            "extra": {
                "custom_intent_items": "[\"trip_planning\",\"沿途感知\"]"
            }
        }
    },
    "vehicle_id":"vehicle 001",
    "chat_id":"chat_abc123456",
    "question_id":"q_001",
    "intent_type":"route_perception",
    "intent_log_id": "log_987654321",
    "history":[
        {
            "q":"前方建筑是什么?",
            "a":"紫贝文化创意港"
        },
        {
            "q":"它的右边是什么?",
            "a":"皇都花园"
        }
    ],
    "car info":{
        "current_location":"上海市闵行区七莘路1599弄",
        "car_heading":150,
        "current_lng_lat":
            {
                "longitude":121.51,
                "latitude":31.30
            }
    }
}
class VolkswagenRoutePerception:
    """沿途感知"""

    def __init__(self, config_info: TestConfigInfoModel, item:EtDataSetItem, task_id, result_id, conf_id, do_user, oem_codes=""):
        # model_params_str = config_info.model_params
        # model_params_dict = json.loads(model_params_str)
        # device_id = model_params_dict.get("Device_Id")
        # vehicle_type = model_params_dict.get("Vehicle_Type")
        # arb_url = model_params_dict.get("arb_url")
        # self.device_id = device_id
        # self.vehicle_type = vehicle_type
        # self.arb_url = arb_url
        self.ak = config_info.model_ak
        self.sk = config_info.model_sk
        self.route_perception_url = config_info.model_url
        self.prompt = item.question
        self.task_id = task_id
        self.result_id = result_id
        self.conf_id = conf_id
        self.item = item
        self.do_user = do_user
        self.oem_codes = oem_codes

    def generate_signature(self,sk: str, method: str, querystr: str, body: Optional[str]) -> str:
        """生成请求签名"""
        try:
            # 对查询参数按字典序排序
            a = querystr.split("&")
            a.sort()
            sortedquerystr = "&".join(a)

            # 构建待签名字符串
            strtosign = method + "\n" + sortedquerystr + "\n"
            if body is not None and len(body) > 0:
                m = hashlib.md5()
                m.update(body.encode("utf8"))
                strtosign += m.hexdigest() + "\n"

            # 计算HMAC-SHA256签名
            h = hmac.new(sk.encode("utf8"), strtosign.encode("utf8"), sha256).digest()
            signature = base64.b64encode(h).decode()
            et_log.info("Generated signature successfully")
            return signature
        except Exception as e:
            error_msg = f"Signature generation failed: {e}"
            et_log.error(error_msg, exc_info=True)

    def route_perception(self):
        first_info_time = None
        start_time = None
        timestamp = int(time.time())
        random_integer = random.randint(0, 65535)
        chat_id = uuid4().hex
        question_id = uuid4().hex
        querystr = f"_timestamp={timestamp}&_nonce={random_integer}&chat_id={chat_id}&question_id={question_id}"
        request_body["query"] = self.prompt  # 先修改字典
        body = json.dumps(request_body, ensure_ascii=False)
        headers = {
            "content-type": "application/json;charset=utf-8"
        }
        try:
            et_log.info(f"intent-url{self.route_perception_url}")
            start_time = time.time()
            response = requests.post(
                f'{self.route_perception_url}?{querystr}',
                headers=headers,
                data=body.encode('utf-8'),
                timeout=20,
                stream=False
            )
            print(f"response.status_code{response.status_code}")
            if response.status_code != 200:
                et_log.error(f"沿途感知接口请求失败，状态码{response.status_code}")
                return None
            response = response.json()
            print(response)
            return first_info_time, response, start_time
        except Exception as e:
            et_log.info(f"处理响应时发生错误: {e}")
            return first_info_time, None, start_time

    def route_perception_result(self):
        consume_time = None
        first_res_time = None
        try:
            et_log.info(
                f"VolkswagenRoutePerception: task_id:{self.task_id} result_id:{self.result_id} conf_id:{self.conf_id}")
            et_log.info(f"query{self.prompt}")
            first_info_time, response, start_time = self.route_perception()
            end_time = time.time()
            if response and first_info_time and start_time:
                first_res_time = round(float(first_info_time - start_time), 2)
                consume_time = round(float(end_time - start_time), 2)
                # 提取tts_text结果
                adapter_result = {}
                tts_text = response.get('tts_text')
                if not tts_text:
                    adapter_result['tts_get_res'] = "接口返回tts为空"
                else:
                    adapter_result['tts_get_res'] = tts_text
                locations = response.get('observation').get('locations')
                locations_len = len(locations)
                if locations_len > 5:
                    adapter_result['location_len_check'] = f"poi数量超过5个：{locations_len}"
                else:
                    adapter_result['location_len_check'] = f"poi数量少于5个：{locations_len}"
                item_result = self.build_result_item(str(self.item._id), self.task_id, self.result_id, "",
                                                     "沿途感知",
                                                     (self.item).expected_answer, response,
                                                     first_res_time, consume_time, "", "",
                                                     self.do_user, self.oem_codes, self.conf_id)
                item_result["result_answer"] = 0  # 回答结果
                if response:
                    item_result["result_answer"] = 1
                    item_result["result_final"] = item_result["result_answer"]  # 最终结果
                else:
                    item_result["result_final"] = 0
                et_log.info(f"route_perception_item_result:{item_result}")
                item_result_id = TestItemResultService.insert_one(item_result)
                et_log.info(f"route_perception:insert_one item_result_id:{item_result_id}")
                return item_result_id
            else:
                raise Exception(f"API调用失败")
        except Exception as e:
            et_log.error(f"API调用失败: {e}")

    def build_result_item(self,item_id, task_id, task_result_id, expected_category, actual_category, expected_answer, actual_answer,
                          first_res_time, qa_use_time, log_id, question_id, do_user=SYS_USER, oem_codes="", conf_id=""):
        et_log.info(f"VolkswagenRoutePerception:build_result_item item_id:{item_id} task_id:{task_id} task_result_id:{task_result_id} "
                    f"conf_id:{conf_id} expected_category:{expected_category} actual_category:{actual_category} "
                    f"expected_answer:{expected_answer} actual_answer:{actual_answer} "
                    f"first_res_time:{first_res_time} qa_use_time:{qa_use_time} log_id:{log_id} question_id:{question_id}")
        if not CTX_OEM_CODES.get() and len(oem_codes) > 0:
            CTX_OEM_CODES.set(oem_codes)
        if not CTX_USER_ID.get():
            CTX_OEM_CODES.set(do_user)
        return {"parent_id":"", "data_set_item_id": str(item_id), "task_id": task_id,
                "task_result_id": str(task_result_id), "actual_task": "", "actual_category": actual_category,
                "actual_answer": actual_answer, "result_answer": 0, "answer_score": 0,
                "qa_recall": 0, "qa_use_time": qa_use_time, "recall_id": log_id,
                "remark": f"log_id:{log_id}|question_id:{question_id}", "first_res_time": first_res_time,
                "do_user": do_user, "re_interval_time": 0, "is_websearch": 0}




def calculate_distance_and_direction(start_lat, start_lon, heading, target_lat, target_lon):
    """
    计算两点间距离和目标相对于车头方向的位置

    参数:
    start_lat (float): 起始点纬度
    start_lon (float): 起始点经度
    heading (float): 车头方向(0-360度)
    target_lat (float): 目标点纬度
    target_lon (float): 目标点经度

    返回:
    tuple: (距离(米), 方向(左前/右前/左后/右后))
    """

    # 使用Haversine公式计算两点间距离
    def haversine_distance(lat1, lon1, lat2, lon2):
        R = 6371000  # 地球半径(米)

        # 转换为弧度
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)

        # Haversine公式
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad
        a = math.sin(dlat / 2) ** 2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon / 2) ** 2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        distance = R * c

        return distance

    # 计算目标点相对于起始点的方位角
    def calculate_bearing(lat1, lon1, lat2, lon2):
        # 转换为弧度
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)

        dlon = lon2_rad - lon1_rad
        y = math.sin(dlon) * math.cos(lat2_rad)
        x = math.cos(lat1_rad) * math.sin(lat2_rad) - math.sin(lat1_rad) * math.cos(lat2_rad) * math.cos(dlon)
        bearing = math.degrees(math.atan2(y, x))
        # 转换为0-360度
        bearing = (bearing + 360) % 360

        return bearing

    # 计算距离
    distance = haversine_distance(start_lat, start_lon, target_lat, target_lon)

    # 计算目标相对于起始点的方位角
    target_bearing = calculate_bearing(start_lat, start_lon, target_lat, target_lon)

    # 计算相对角度(目标方位角减去车头方向)
    relative_angle = (target_bearing - heading + 360) % 360

    # 判断方向，增加前后左右
    if 0 <= relative_angle < 22.5 or 337.5 <= relative_angle < 360:
        direction = "前"
    elif 22.5 <= relative_angle < 67.5:
        direction = "右前"
    elif 67.5 <= relative_angle < 112.5:
        direction = "右"
    elif 112.5 <= relative_angle < 157.5:
        direction = "右后"
    elif 157.5 <= relative_angle < 202.5:
        direction = "后"
    elif 202.5 <= relative_angle < 247.5:
        direction = "左后"
    elif 247.5 <= relative_angle < 292.5:
        direction = "左"
    elif 292.5 <= relative_angle < 337.5:
        direction = "左前"
    else:
        direction = "未知"

    return distance, direction


# 使用示例
if __name__ == "__main__":
    # 示例：从北京天安门出发，车头朝北，目标是故宫
    start_latitude = 31.30  # 天安门纬度
    start_longitude = 121.51  # 天安门经度
    car_heading = 150  # 车头朝北(0度)
    target_latitude = 30.015994  # 故宫纬度
    target_longitude = 118.049828  # 故宫经度

    distance, direction = calculate_distance_and_direction(
        start_latitude, start_longitude, car_heading,
        target_latitude, target_longitude
    )

    print(f"距离: {distance:.2f} 米")
    print(f"方向: {direction}")


    class ConfigInfo:
        model_ak = "MjYyNjE5MjE1MzE5MzM5NjE5"
        model_sk = "MjYyNjE5MjE1MzE5MzM5NjE5"
        model_url = "https://agent-custom.extour-inc.com:8888/api/route/perception"

    item = {"question": "规划下去上海的路线"}
    tool = VolkswagenRoutePerception(ConfigInfo, item, "", "", "", "", "")
    tool.route_perception_result()