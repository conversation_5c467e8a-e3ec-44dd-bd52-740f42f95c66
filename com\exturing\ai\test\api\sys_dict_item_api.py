# api.py

from flask import Blueprint, request
from com.exturing.ai.test.model.sys_dict_item import (
    SysDictItemModel,
    SysDictItemQueryModel,
)
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.sys_dict_item_service import (
    create,
    query_page,
    query_list,
    update,
    delete,
)

sys_dict_item = Blueprint("sys_dict_item", __name__)


# 新增系统字典项
@sys_dict_item.route(f"/{URL_PREFIX}/staff/sys-dict-item/create", methods=["POST"])
def sys_dict_item_create():
    et_log.info("############sys_dict_item_create################")
    req_data = request.get_json()
    sys_dict_item_instance = SysDictItemModel(**req_data)

    try:
        sys_dict_item_id = create(sys_dict_item_instance)

        return ApiResult(
            ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(sys_dict_item_id)
        ).to_json()
    except AssertionError as e:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, str(e), "").to_json()


# 系统字典项分页查询
@sys_dict_item.route(f"/{URL_PREFIX}/staff/sys-dict-item/page", methods=["POST"])
def sys_dict_item_page():
    et_log.info("############sys_dict_item_page################")
    data = request.get_json()
    query = SysDictItemQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()
    ).to_json()


# 系统字典项列表查询
@sys_dict_item.route(f"/{URL_PREFIX}/staff/sys-dict-item/list", methods=["POST"])
def sys_dict_item_list():
    et_log.info("############sys_dict_item_list################")
    data = request.get_json()
    query = SysDictItemQueryModel(**data)

    data_list = query_list(query)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, data_list
    ).to_json()


# 修改系统字典项
@sys_dict_item.route(f"/{URL_PREFIX}/staff/sys-dict-item/update", methods=["POST"])
def sys_dict_item_update():
    et_log.info("############sys_dict_item_update################")
    req_data = request.get_json()
    id = req_data.get("sys_dict_item_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "sys_dict_item_id is null", ""
        ).to_json()

    sys_dict_item_instance = SysDictItemModel(**req_data)

    try:
        update_result = update(id, sys_dict_item_instance)

        return ApiResult(
            ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)
        ).to_json()
    except AssertionError as e:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, str(e), "").to_json()


# 删除系统字典项
@sys_dict_item.route(f"/{URL_PREFIX}/staff/sys-dict-item/del", methods=["POST"])
def sys_dict_item_delete():
    et_log.info("############sys_dict_item_delete################")
    req_data = request.get_json()
    id = req_data.get("sys_dict_item_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "sys_dict_item_id is null", ""
        ).to_json()

    delete_result = delete(id)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result
    ).to_json()
