from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class SysDictItemModel(BaseDataModel):
    parent_id: Optional[str] = None  # 父级字典id
    dict_code: str  # 字典编码
    name: str  # 字典名称
    code: str | int  # 字典编码
    order: Optional[int] = None  # 排序
    desc: Optional[str] = None  # 字典描述
    is_disabled: Optional[bool] = None  # 是否禁用


class SysDictItemQueryModel(BaseModel):
    is_disabled: Optional[bool] = None  # 是否禁用
    dict_code: Optional[str] = None

    def get_query_condition(self):
        condition = {}

        if self.is_disabled is not None:
            condition["is_disabled"] = self.is_disabled
        if self.dict_code:
            condition["dict_code"] = self.dict_code

        return condition
