from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class ProjectRequirementModel(BaseDataModel):
    name: Optional[str] = None  # 需求名称
    type: Optional[str] = None  # 需求类型
    parent_id: Optional[str] = None  # 父级需求id
    project_id: Optional[str] = None  # 项目id
    content: Optional[str] = None  # 需求内容
    priority: Optional[str] = None  # 需求优先级 P0：高 P1：中 P2：低
    remark: Optional[str] = None  # 需求备注
    is_main: Optional[bool] = None  # 是否主线产品
    contractor: Optional[str] = None  # 承接方

    is_origin: Optional[bool] = None  # 是否原始需求
    attribution: Optional[str] = None  # 业务归属 - 原始需求
    time_target: Optional[str] = None  # 交付时间目标 - 原始需求
    assess_res: Optional[str] = None  # 评估结果 - 原始需求
    confirm_remark: Optional[str] = None  # 确认情况 - 原始需求


class ProjectRequirementQueryModel(BaseModel):
    name: Optional[str] = None
    type: Optional[int] = None
    project_id: Optional[str] = None
    parent_id: Optional[str] = None
    priority: Optional[str] = None
    is_main: Optional[bool] = None
    is_origin: Optional[bool] = None

    def get_query_condition(self):
        condition = {}

        if self.name:
            condition["name"] = {"$regex": str(self.name).strip(), "$options": "i"}

        if self.type:
            condition["type"] = self.type

        if self.project_id:
            condition["project_id"] = self.project_id

        if self.parent_id:
            condition["parent_id"] = self.parent_id

        if self.priority:
            condition["priority"] = self.priority

        if self.is_main is not None:
            condition["is_main"] = self.is_main

        if self.is_origin is not None:
            condition["is_origin"] = self.is_origin

        return condition

    def get_query_tree_condition(self):
        condition = {}

        if self.project_id:
            condition["project_id"] = self.project_id
        if self.is_origin is not None:
            condition["is_origin"] = self.is_origin

        return condition
