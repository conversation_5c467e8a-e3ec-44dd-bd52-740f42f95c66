from datetime import datetime
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
import sys

# 落域数据
category_list = [
    ["D", "车辆控制指令", 2],
    ["O", "导航自由说", 1],
    ["Z", "拒识", 1],
    ["C", "壁纸生成", 1],
    ["G", "场景生成", 1],
    ["W", "体育", 1],
    ["M", "音乐", 1],
    ["E", "查询车辆说明书及用车百科", 1],
    ["Q", "美食自由搜", 1],
    ["K", "新闻", 1],
    ["T", "氛围灯设置", 2],
    ["H", "日程提醒", 2],
    ["I", "POI识别", 2],
    ["J", "车辆功能查询", 2],
    ["N", "车况查询", 2],
    ["L", "短视频", 1],
    ["U", "酒店自由搜", 1],
    ["B", "旅游规划", 1],
    ["Y", "敏感信息", 1],
    ["P", "视频自由搜", 1],
    ["e", "教育", 1],
    ["A", "闲聊及百科大问答", 1],
    ["F", "菜谱", 1],
    ["R", "指示灯", 2],
    ["S", "景点自由搜", 1],
]

# 模块维度数据
model_dimension_list = {
    "新闻助手": [
        "政治新闻",
        "文化新闻",
        "科技新闻",
        "军事新闻",
        "娱乐新闻",
        "财经新闻",
        "社会新闻",
        "体育新闻",
        "汽车新闻",
        "国外新闻",
    ],
    "景点助手": [
        "国内总览",
        "景区推荐",
        "亲子活动",
        "家庭出游",
        "休闲放松",
        "自然探索",
        "文化体验",
        "经济旅游",
        "夜间活动",
        "交通便捷",
        "特殊兴趣",
    ],
    "日程提醒": [
        "日程时间提醒",
        "日程地点提醒",
        "日程事务提醒",
    ],
    "绘画助手": [
        "人物肖像",
        "风景自然",
        "城市建筑",
        "动物植物",
        "抽象艺术",
        "科幻幻想",
        "历史场景",
        "文化艺术",
        "体育竞技",
        "交通工具",
        "数码艺术",
        "卡通漫画",
        "传统绘画",
        "现代艺术",
        "极简主义",
    ],
    "音乐自由说": [
        "榜单维度",
        "地域维度",
        "歌词维度",
        "歌手/歌名维度",
        "歌名维度",
        "情感主题维度",
        "社交热度维度",
        "时间维度",
        "使用场景维度",
        "音乐传承维度",
        "音乐创新维度",
        "音乐的节日氛围维度",
        "音乐风格维度",
        "音乐纪念维度",
        "音乐教育流派维度",
        "音乐节维度",
        "音乐空间维度",
        "音乐色彩维度(视觉化相关)",
        "音乐商业维度",
        "音乐社交互动维度",
        "音乐受众维度",
        "音乐与地理地标维度",
        "音乐与健康维度",
        "音乐与科技发明维度",
        "音乐与科技结合维度",
        "音乐与历史时期维度",
        "音乐与旅行目的地维度",
        "音乐与美食维度",
        "音乐与时尚秀场维度",
        "音乐与特殊群体维度",
        "音乐与天文现象维度",
        "音乐与文学作品维度",
        "音乐与舞蹈风格维度",
        "音乐与艺术跨界维度",
        "音乐与游戏维度",
        "音乐与宗教维度",
        "音乐元素维度",
        "音乐制作维",
        "音乐与自然元素维度",
    ],
    "百科助手": [
        "讲故事",
        "诗词",
        "作文",
        "人物百科",
        "生活常识",
        "世界之最",
        "十万个为什么",
        "笑话",
        "天文",
        "地理",
        "数学",
        "物理",
        "化学",
        "历史",
        "植物",
        "计算机",
        "医疗健康",
        "法律",
        "金融",
        "英语",
        "聊天",
        "职场",
        "菜谱",
    ],
    "氛围助手": [
        "颜色调整维度",
        "开关控制维度",
        "亮度调节维度",
        "模式选择维度",
        "场景生成",
    ],
    "用车助手": [
        "查询车辆说明书及用车百科",
        "车况查询",
        "车辆功能查询",
        "车辆控制指令",
    ],
    "行程助手": [
        "路线安排",
        "旅游攻略",
        "美食计划",
        "住宿规划",
        "国外行程",
    ],
    "美食助手": [
        "美食做法",
        "节日推荐",
        "特定场合",
        "口味菜系",
        "特定地点",
        "指定需求",
        "价格环境",
        "品牌餐饮",
    ],
    "影音助手": [
        "音乐自由说",
        "短视频",
        "视频自由说",
    ],
    "氛围助手": [
        "氛围灯",
    ],
    "场景生成": [
        "场景生成",
    ],
}

# 标签数据
tag_list = [
    "有效数据",
    "无效数据",
    "生产数据",
    "测试数据",
]

dict_list = [
    {
        "name": "业务归属",
        "code": "business_attribution",
        "items": [
            {"name": "非交付范围", "code": "非交付范围"},
            {"name": "车书", "code": "车书"},
            {"name": "主动服务", "code": "主动服务"},
            {"name": "字节公版", "code": "字节公版"},
        ],
    },
    {
        "name": "评估结论",
        "code": "assessment_conclusion",
        "items": [
            {"name": "非弋途交付范围", "code": "非弋途交付范围"},
            {"name": "复用", "code": "复用"},
            {"name": "复用+定制", "code": "复用+定制"},
            {"name": "定制", "code": "定制"},
        ],
    },
    {
        "name": "交付时间目标",
        "code": "delivery_time_target",
        "items": [
            {"name": "无需交付", "code": "无需交付"},
            {"name": "首批", "code": "首批"},
        ],
    },
    {
        "name": "项目集",
        "code": "project_group",
        "items": [
            {"name": "字节", "code": "字节"},
            {"name": "长城", "code": "长城"},
            {"name": "北汽", "code": "北汽"},
            {"name": "智己", "code": "智己"},
            {"name": "奔腾", "code": "奔腾"},
            {"name": "奇瑞", "code": "奇瑞"},
            {"name": "华为", "code": "华为"},
            {"name": "深圳", "code": "深圳"},
            {"name": "思必驰", "code": "思必驰"},
            {"name": "塞力斯", "code": "塞力斯"},
            {"name": "SOA", "code": "SOA"},
        ],
    },
    {
        "name": "项目状态",
        "code": "project_status",
        "items": [
            {"name": "待启动", "code": "待启动"},
            {"name": "执行中", "code": "执行中"},
            {"name": "已报价待启动", "code": "已报价待启动"},
            {"name": "关闭", "code": "关闭"},
            {"name": "待详细信息输入", "code": "待详细信息输入"},
            {"name": "BD中", "code": "BD中"},
        ],
    },
    {
        "name": "项目性质",
        "code": "project_stage",
        "items": [
            {"name": "量产", "code": "量产"},
            {"name": "POC", "code": "POC"},
            {"name": "demo", "code": "demo"},
        ],
    },
    {
        "name": "风险等级",
        "code": "risk_level",
        "items": [
            {"name": "低", "code": "低"},
            {"name": "中", "code": "中"},
            {"name": "高", "code": "高"},
        ],
    },
    {
        "name": "风险类型",
        "code": "risk_type",
        "items": [
            {"name": "沟通成本高", "code": "沟通成本高"},
            {"name": "技术难度高", "code": "技术难度高"},
            {"name": "人力资源型", "code": "人力资源型"},
        ],
    },
    {
        "name": "项目模式",
        "code": "project_mode",
        "items": [
            {"name": "主线适配", "code": "主线适配"},
            {"name": "客户定制", "code": "客户定制"},
            {"name": "主线+定制", "code": "主线+定制"},
        ],
    },
    {
        "name": "商务状态",
        "code": "business_status",
        "items": [
            {"name": "RFQ", "code": "RFQ"},
            {"name": "TR", "code": "TR"},
            {"name": "定点中", "code": "定点中"},
            {"name": "已报价", "code": "已报价"},
            {"name": "已签署", "code": "已签署"},
            {"name": "已暂停", "code": "已暂停"},
            {"name": "回款中", "code": "回款中"},
        ],
    },
    {
        "name": "收入性质",
        "code": "income_type",
        "items": [
            {"name": "NRE", "code": "NRE"},
            {"name": "LIC", "code": "LIC"},
            {"name": "差旅", "code": "差旅"},
            {"name": "运维", "code": "运维"},
            {"name": "云资源", "code": "云资源"},
        ],
    },
    {
        "name": "交付状态",
        "code": "delivery_status",
        "items": [
            {"name": "CC", "code": "CC"},
            {"name": "RC", "code": "RC"},
            {"name": "SOP", "code": "SOP"},
            {"name": "OTA", "code": "OTA"},
        ],
    },
    {
        "name": "主线产品交付状态",
        "code": "mainline_delivery_status",
        "items": [
            {"name": "产品定义中", "code": "产品定义中"},
            {"name": "研发中", "code": "研发中"},
            {"name": "已完成", "code": "已完成"},
            {"name": "维护中", "code": "维护中"},
            {"name": "已暂停", "code": "已暂停"},
        ],
    },
    {
        "name": "项目类型",
        "code": "project_type",
        "item_code_type": 2,
        "items": [
            {"name": "内部项目", "code": 1},
            {"name": "外部项目", "code": 2},
        ],
    },
    {
        "name": "员工职级",
        "code": "employee_level",
        "items": [
            {"name": "P4", "code": "P4"},
            {"name": "P5", "code": "P5"},
            {"name": "P6", "code": "P6"},
            {"name": "P7", "code": "P7"},
            {"name": "P8", "code": "P8"},
        ],
    },
    {
        "name": "员工类型",
        "code": "employee_type",
        "items": [
            {"name": "外包", "code": "外包"},
            {"name": "实习生", "code": "实习生"},
            {"name": "正式", "code": "正式"},
        ],
    },
    {
        "name": "员工技术栈",
        "code": "employee_stack",
        "items": [
            {"name": "Android", "code": "Android"},
            {"name": "python", "code": "python"},
            {"name": "C++", "code": "C++"},
            {"name": "java", "code": "java"},
            {"name": "前端开发", "code": "前端开发"},
            {"name": "测试", "code": "测试"},
            {"name": "算法", "code": "算法"},
        ],
    },
    {
        "name": "员工BASE地",
        "code": "employee_base",
        "items": [
            {"name": "北京", "code": "北京"},
            {"name": "上海", "code": "上海"},
            {"name": "合肥", "code": "合肥"},
            {"name": "深圳", "code": "深圳"},
            {"name": "苏州", "code": "苏州"},
        ],
    },
    {
        "name": "员工部门",
        "code": "employee_department",
        "items": [
            {"name": "AI算法中心", "code": "AI算法中心"},
            {"name": "AI工程中心", "code": "AI工程中心"},
            {"name": "AI应用中心", "code": "AI应用中心"},
            {"name": "AI质效中心", "code": "AI质效中心"},
            {"name": "AI基础软件中心", "code": "AI基础软件中心"},
            {"name": "产品部", "code": "产品部"},
        ],
    },
    {
        "name": "员工学历",
        "code": "employee_education",
        "items": [
            {"name": "本科", "code": "本科"},
            {"name": "硕士", "code": "硕士"},
            {"name": "博士", "code": "博士"},
        ],
    },
    {
        "name": "员工潜力",
        "code": "employee_potential",
        "items": [
            {"name": "高", "code": "高"},
            {"name": "中", "code": "中"},
            {"name": "低", "code": "低"},
        ],
    },
    {
        "name": "入职状态",
        "code": "onboard_status",
        "items": [
            {"name": "在职", "code": "在职"},
            {"name": "待入职", "code": "待入职"},
            {"name": "拒绝入职", "code": "拒绝入职"},
            {"name": "offer沟通中", "code": "offer沟通中"},
            {"name": "offer审批中", "code": "offer审批中"},
            {"name": "offer拒绝", "code": "offer拒绝"},
            {"name": "offer待沟通", "code": "offer待沟通"},
            {"name": "已离职", "code": "已离职"},
        ],
    },
    {
        "name": "优先级",
        "code": "priority",
        "items": [
            {"name": "P0", "code": "P0"},
            {"name": "P1", "code": "P1"},
            {"name": "P2", "code": "P2"},
        ],
    },
    {
        "name": "员工角色",
        "code": "employee_role",
        "items": [
            {"name": "产品", "code": "产品"},
            {"name": "UI/UE", "code": "UI/UE"},
            {"name": "云端", "code": "云端"},
            {"name": "端侧", "code": "端侧"},
            {"name": "测试", "code": "测试"},
            {"name": "运维", "code": "运维"},
            {"name": "架构", "code": "架构"},
            {"name": "PM", "code": "PM"},
            {"name": "商务", "code": "商务"},
            {"name": "后台支撑", "code": "后台支撑"},
            {"name": "驻场", "code": "驻场"},
        ],
    },
]


def del_data(oem: str):
    """删除数据"""

    db = MongoDBUtil().mdb
    db["oem"].delete_many({"oem_codes": oem})
    db["vehicle"].delete_many({"oem_codes": oem})
    db["category_info"].delete_many({"oem_codes": oem})
    db["dic_info"].delete_many({"oem_codes": oem})
    db["data_tag"].delete_many({"oem_codes": oem})


def init_data(oem: str, vehicle: str):
    """初始化数据"""

    db = MongoDBUtil().mdb

    common_dict = {
        "oem_codes": oem,
        "is_del": 0,
        "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "update_time": "",
        "create_by": 0,
        "update_by": 0,
    }

    # 1. 初始化OEM数据
    oem_dict = {
        "oem_name": oem,
        "code": oem,
    } | common_dict

    db["oem"].insert_one(oem_dict)

    # 2. 初始化车型数据
    vehicle_dict = {
        "vehicle_name": vehicle,
        "pid": vehicle,
        "oem_code": oem,
    } | common_dict

    db["vehicle"].insert_one(vehicle_dict)

    # 3. 初始化落域数据
    category_dict_list = []
    for category in category_list:
        category_dict = {
            "category_name": category[1],
            "category_code": category[0],
            "category_type": category[2],
        } | common_dict

        category_dict_list.append(category_dict)

    db["category_info"].insert_many(category_dict_list)

    # 4. 初始化字典数据
    for model in model_dimension_list:
        model_dict = {
            "name": model,
            "group_code": "dm-model",
            "group_name": "模块",
        } | common_dict

        model_res = db["dic_info"].insert_one(model_dict)

        dimension_dict_list = []
        for dimension in model_dimension_list[model]:
            dimension_dict = {
                "pid": model_res.inserted_id,
                "name": dimension,
                "group_code": "dm-dimension",
                "group_name": "维度",
            } | common_dict
            dimension_dict_list.append(dimension_dict)

        db["dic_info"].insert_many(dimension_dict_list)

    # 5. 初始化标签数据
    tag_dict_list = []
    for tag in tag_list:
        tag_dict = {
            "tag_name": tag,
            "type": 1,
            "status": 1,
        } | common_dict

        tag_dict_list.append(tag_dict)

    db["data_tag"].insert_many(tag_dict_list)


def init_dict_data(oem: str):
    """初始化字典数据"""
    db = MongoDBUtil().mdb

    common_dict = {
        "oem_codes": oem,
        "is_del": 0,
        "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "update_time": "",
        "create_by": 0,
        "update_by": 0,
    }

    for dict in dict_list:
        dict_dict = {
            "name": dict["name"],
            "code": dict["code"],
            "item_code_type": dict.get("item_code_type", 1),
        } | common_dict

        db["sys_dict"].insert_one(dict_dict)

        item_dict_list = []
        for item in dict["items"]:
            item_dict = {
                "dict_code": dict["code"],
                "name": item["name"],
                "code": item["code"],
            } | common_dict

            item_dict_list.append(item_dict)

        db["sys_dict_item"].insert_many(item_dict_list)


def comfirm_run():
    """确认是否执行"""
    while True:
        comfirm = input("运行脚本会清空当前OEM数据，确认是否执行(y/n):").strip().lower()
        if comfirm in ("y", "yes"):
            return True
        else:
            print("操作已取消。")
            sys.exit()


if __name__ == "__main__":
    # comfirm_run()

    # 新的OEM客户，需要初始化数据
    oem = "demo"  # oem代码
    vehicle = "demo"  # 车型pid

    # 1. 删除数据
    # del_data(oem)
    # 2. 初始化数据
    # init_data(oem, vehicle)
    # 初始化字典数据
    # init_dict_data("all")

# python -m com.exturing.ai.test.init.oem_data_init
