from datetime import datetime

from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import List, Optional
from pydantic import BaseModel


class CustomerModel(BaseModel):
    oem: Optional[str] = None  # OEM客户
    tier1: Optional[str] = None  # 一级供应商


class RiskModel(BaseModel):
    level: Optional[str] = None  # 风险等级
    type: Optional[str] = None  # 风险类型
    key: Optional[str] = None  # 风险关键点
    response: Optional[str] = None  # 应对策略


class OwnerModel(BaseModel):
    business_owner: Optional[str] = None  # 商务负责人
    pm_owner: Optional[str] = None  # 项目经理
    test_owner: Optional[str] = None  # 测试负责人
    dev_owner: Optional[str] = None  # 研发负责人
    product_owner: Optional[str] = None  # 产品负责人


class MilestoneModel(BaseModel):
    status: Optional[str] = None  # 交付状态
    complete_percent: Optional[float] = None  # 完成度
    start_time: Optional[str] = None  # 启动时间
    cc_time: Optional[str] = None  # CC时间
    rc_time: Optional[str] = None  # RC时间
    end_time: Optional[str] = None  # 结项时间


class QuoteModel(BaseModel):
    lic: Optional[float] = None  # 许可报价
    nre: Optional[float] = None  # NRE报价
    travel: Optional[float] = None  # 差旅费用
    quantity: Optional[int] = None  # 数量 台套数
    other: Optional[float] = None  # 其他费用


class BusinessModel(BaseModel):
    status: Optional[str] = None  # 商务状态
    estimated_quote: Optional[QuoteModel] = None  # 预计报价
    actual_quote: Optional[QuoteModel] = None  # 实际报价
    customer_budget: Optional[float] = None  # 客户预算
    contract_amount: Optional[float] = None  # 合同金额
    actual_incurred_cost_op: Optional[float] = None  # 实际发生成本（仅分摊运营费用）
    actual_incurred_cost_all: Optional[float] = None  # 实际发生成本（分摊全部费用）
    gap: Optional[float] = None  # 预算差额


class MainProjectDependencyModel(BaseModel):
    project_id: Optional[str] = None  # 主线项目ID


class ProjectModel(BaseDataModel):
    name: Optional[str] = None  # 项目名称
    code: Optional[str] = None  # 项目编码
    type: Optional[int] = None  # 项目类型 1 内部项目 2 外部项目 3 其他
    start_time: Optional[str] = None  # 项目开始时间
    end_time: Optional[str] = None  # 项目结束时间
    desc: Optional[str] = None  # 项目描述

    parent_id: Optional[str] = None  # 父级项目id
    group: Optional[str] = None  # 项目集
    stage: Optional[str] = None  # 项目性质 POC/量产
    mode: Optional[str] = None  # 项目模式 主线适配/客户定制/主线+定制
    status: Optional[str] = None  # 项目状态
    vehicle_list: Optional[str] = None  # 车型信息
    customer: Optional[CustomerModel] = None  # 客户信息
    risk: Optional[RiskModel] = None  # 风险信息
    owner: Optional[OwnerModel] = None  # 负责人信息
    milestone: Optional[MilestoneModel] = None  # 项目节点
    business: Optional[BusinessModel] = None  # 商务信息
    project_dependency: Optional[List[MainProjectDependencyModel]] = (
        None  # 主线项目依赖
    )


class ProjectQueryModel(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None  # 项目编码
    type: Optional[int] = None
    is_valid: Optional[bool] = None  # 是否有效

    def get_query_condition(self):
        condition = {}
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if self.name:
            condition["name"] = {"$regex": str(self.name).strip(), "$options": "i"}
        if self.code:
            condition["code"] = {"$regex": str(self.code).strip(), "$options": "i"}
        if self.type:
            condition["type"] = self.type

        if self.is_valid is not None:
            if self.is_valid:
                condition["start_time"] = {"$lte": current_time}
                condition["end_time"] = {"$gte": current_time}
            else:
                condition["start_time"] = {"$gt": current_time}
                condition["end_time"] = {"$lt": current_time}

        return condition
